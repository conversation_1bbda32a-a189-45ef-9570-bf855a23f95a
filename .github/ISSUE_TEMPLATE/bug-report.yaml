name: Bug Report
description: Report a bug encountered while operating vcluster
labels: 
- kind/bug
body:
  - type: textarea
    id: problem
    attributes:
      label: What happened?
      description: |
        Please provide as much info as possible. Not doing so may result in your bug not being addressed in a timely manner.
    validations:
      required: true

  - type: textarea
    id: expected
    attributes:
      label: What did you expect to happen?
    validations:
      required: true

  - type: textarea
    id: reproduce
    attributes:
      label: How can we reproduce it (as minimally and precisely as possible)?
    validations:
      required: true

  - type: textarea
    id: additional
    attributes:
      label: Anything else we need to know?

  - type: textarea
    id: k8sVersion
    attributes:
      label: Host cluster Kubernetes version
      value: |
        <details>
        
        ```console
        $ kubectl version
        # paste output here
        ```

        </details>
    validations:
      required: true

  - type: textarea
    id: vclusterVersion
    attributes:
      label: vcluster version
      value: |
        <details>
        
        ```console
        $ vcluster --version
        # paste output here
        ```

        </details>
    validations:
      required: true

  - type: textarea
    id: vclusterConfig
    attributes:
      label: VCluster Config
      value: |
        <details>

        ```
        # My vcluster.yaml / values.yaml here
        ```

        </details>
    validations:
      required: true
  
