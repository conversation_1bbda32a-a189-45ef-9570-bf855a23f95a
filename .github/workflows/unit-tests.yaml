name: Unit tests

on:
  release:
    types: [created]
  pull_request:
    branches:
      - main
      - v*
    paths:
      - go.mod
      - go.sum
      - "**.go"
      - "!test/**" # exclude changes in e2e tests
      - ".github/workflows/unit-tests.yaml"
      - "hack/test.sh"
      - "chart/**"

concurrency:
  group: ${{ github.workflow }}-${{ github.event.pull_request.number || github.ref }}
  cancel-in-progress: true

jobs:
  helm-unit-tests:
    name: Execute all helm tests
    if: github.repository_owner == 'loft-sh' # do not run on forks
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v5
      - name: Install Helm Unit Test Plugin
        run: |
          helm plugin install https://github.com/helm-unittest/helm-unittest --version v0.4.4
      - name: Run Helm Unit Tests
        run: |
          helm unittest chart

  go-unit-test:
    name: Execute all go tests
    if: github.repository_owner == 'loft-sh' # do not run on forks
    runs-on: ubuntu-22.04
    steps:
      - name: Check out code into the Go module directory
        uses: actions/checkout@v5
      - name: Set up Go
        uses: actions/setup-go@v6
        with:
          go-version-file: go.mod
          cache: false
      - name: Execute unit tests
        run: ./hack/test.sh
