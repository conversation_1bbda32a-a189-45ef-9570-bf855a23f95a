name: Publish Chart

on:
  workflow_call:
    inputs:
      release_version:
        description: "The release version to publish"
        required: true
        type: string
      ref:
        description: "The git ref to checkout"
        required: true
        type: string
  workflow_dispatch:

jobs:
  publish-chart:
    runs-on: ubuntu-22.04

    steps:
      - name: Validate semantic version
        id: semver
        uses: loft-sh/github-actions/.github/actions/semver-validation@semver-validation/v1
        with:
          version: "${{ inputs.release_version }}"
      - name: Check validation result
        run: '[[ "${{ steps.semver.outputs.is_valid }}" == "true" ]] || (echo "Invalid version: ${{ inputs.release_version }}" && exit 1)'
      - name: Check out code
        uses: actions/checkout@v5
        with:
          ref: ${{ inputs.ref }}
      - uses: azure/setup-helm@v4
        with:
          version: "v3.0.2"
      - name: Publish Helm chart
        run: |
          set -euo pipefail
          CHART_VERSION=$(echo "${{ inputs.release_version }}" | sed -nE 's!^v(.*)!\1!p')
          echo "Publishing Helm chart for version $CHART_VERSION"
          helm plugin install https://github.com/chartmuseum/helm-push.git || true
          helm repo add chartmuseum "$CHART_MUSEUM_URL" --username "$CHART_MUSEUM_USER" --password "$CHART_MUSEUM_PASSWORD"
          helm cm-push --force --version="$CHART_VERSION" --app-version="$CHART_VERSION" chart chartmuseum
        env:
          CHART_MUSEUM_URL: "https://charts.loft.sh/"
          CHART_MUSEUM_USER: ${{ secrets.CHART_MUSEUM_USER }}
          CHART_MUSEUM_PASSWORD: ${{ secrets.CHART_MUSEUM_PASSWORD }}
