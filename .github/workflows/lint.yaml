name: Lint

on:
  pull_request:
    branches:
      - main
      - v*
    paths:
      - go.mod
      - go.sum
      - "**.go"
      - ".github/workflows/lint.yaml"

concurrency:
  group: ${{ github.workflow }}-${{ github.event.pull_request.number || github.ref }}
  cancel-in-progress: true

jobs:
  golangci:
    name: lint
    if: github.repository_owner == 'loft-sh' # do not run on forks
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v5

      - name: Check for prohibited vcluster import in config dir
        run: |
          #!/bin/bash
          set -e

          IMPORT="github.com/loft-sh/vcluster"
          PATTERN="\"$IMPORT(\"|/)"
          FOUND_FILES=$(find ./config -name "*.go" -not -path "./config/legacyconfig/*" | xargs grep -l -E "$PATTERN" 2>/dev/null || true)

          if [ -n "$FOUND_FILES" ]; then
            echo "❌ ERROR: Prohibited import prefix '$IMPORT' found in:"
            echo "$FOUND_FILES"
            exit 1
          fi

      - uses: actions/setup-go@v6
        with:
          go-version-file: ./go.mod
          cache: false

      - name: Generate Embedded Helm Chart
        run: |
          go generate ./...

      - name: Verify schema changes
        run: |
          VALUES_SHA=$(cat chart/values.yaml | sha256sum)
          VALUES_SCHEMA_SHA=$(cat chart/values.schema.json | sha256sum)

          go run hack/schema/main.go

          VALUES_SHA_AFTER=$(cat chart/values.yaml | sha256sum)
          VALUES_SCHEMA_SHA_AFTER=$(cat chart/values.schema.json | sha256sum)

          # if there are changes, tell developer to run script
          if [ "$VALUES_SHA" != "$VALUES_SHA_AFTER" ] || [ "$VALUES_SCHEMA_SHA" != "$VALUES_SCHEMA_SHA_AFTER" ]; then
            echo "Seems like you forgot to run 'go run hack/schema/main.go' before committing your changes!"
            exit 1
          fi

      - name: Run golangci-lint
        uses: golangci/golangci-lint-action@v8
        with:
          version: v2.4
