name: Build and push head images

on:
  push:
    branches:
      - main

jobs:
  helm-unit-tests:
    name: Execute all helm tests
    if: github.repository_owner == 'loft-sh' # do not run on forks
    runs-on: ubuntu-latest
    permissions:
      contents: read
    steps:
      - name: Checkout
        uses: actions/checkout@v5
      - name: Install Helm Unit Test Plugin
        run: |
          helm plugin install https://github.com/helm-unittest/helm-unittest --version v0.4.4
      - name: Run Helm Unit Tests
        run: |
          helm unittest chart

  go-unit-test:
    name: Execute all go tests
    if: github.repository_owner == 'loft-sh' # do not run on forks
    runs-on: ubuntu-22.04
    permissions:
      contents: read
    steps:
      - name: Check out code into the Go module directory
        uses: actions/checkout@v5
      - name: Set up Go
        uses: actions/setup-go@v6
        with:
          go-version-file: go.mod
          cache: false
      - name: Execute unit tests
        run: ./hack/test.sh

  build-head-images:
    runs-on: ubuntu-latest
    needs: [helm-unit-tests, go-unit-test]
    name: Publish head images
    if: |
      github.repository_owner == 'loft-sh' &&
      github.ref_name == 'main'
    permissions:
      contents: read
      id-token: write # This is the key for OIDC cosign!
      packages: write
    steps:
      - name: Checkout repository
        uses: actions/checkout@v5
        with:
          fetch-depth: 0

      - name: Set up Go
        uses: actions/setup-go@v6
        with:
          cache: false
          go-version-file: go.mod

      - name: Install just
        uses: extractions/setup-just@v1

      - name: Set up QEMU
        uses: docker/setup-qemu-action@v3

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Setup Cosgin
        uses: sigstore/cosign-installer@main
        with:
          cosign-release: "v2.2.3"

      - name: Log in to GitHub Container Registry
        uses: docker/login-action@v3
        with:
          registry: ghcr.io
          username: ${{ github.actor }}
          password: ${{ secrets.GITHUB_TOKEN }}

      - name: Setup GoReleaser and Build Images
        uses: goreleaser/goreleaser-action@v6
        with:
          distribution: goreleaser
          version: latest
          args: release --config .goreleaser.head_images.yaml --clean --skip=announce,archive,validate
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
          TELEMETRY_PRIVATE_KEY: ""
          CI_BRANCH: ${{ github.ref_name }}
