name: Notify Release

on:
  workflow_call:
    inputs:
      release_version:
        description: "The release version to notify about"
        required: true
        type: string
      previous_tag:
        description: "The previous tag for comparison"
        required: true
        type: string
      ref:
        description: "The git ref to checkout"
        required: true
        type: string

jobs:
  notify_release:
    runs-on: ubuntu-22.04
    steps:
      - uses: actions/checkout@v5
        with:
          fetch-depth: 0
          ref: ${{ inputs.ref }}
      - id: get_base_branch
        name: Get base branch for tag
        run: |
          RELEASE_VERSION="${{ inputs.release_version }}"
          TAG_COMMIT=$(git rev-list -n 1 "$RELEASE_VERSION")

          # Find all remote branches containing this commit
          REMOTE_BRANCHES=$(git branch -r --contains "$TAG_COMMIT" | grep -v HEAD | sed -e 's/^[[:space:]]*//' -e 's/^origin\///')

          if [ -z "$REMOTE_BRANCHES" ]; then
            BASE_BRANCH="main"
          else
            BEST_BRANCH="main"
            BEST_DISTANCE=99999
            for REMOTE_BRANCH in $REMOTE_BRANCHES; do
              [ -z "$REMOTE_BRANCH" ] && continue
              BRANCH_BASE=$(git merge-base origin/main origin/$REMOTE_BRANCH 2>/dev/null || echo "")
              if [ ! -z "$BRANCH_BASE" ]; then
                if git merge-base --is-ancestor "$BRANCH_BASE" "$TAG_COMMIT" 2>/dev/null; then
                  DISTANCE=$(git rev-list --count "$TAG_COMMIT..origin/$REMOTE_BRANCH")
                  if [ "$DISTANCE" -lt "$BEST_DISTANCE" ]; then
                    BEST_BRANCH=$REMOTE_BRANCH
                    BEST_DISTANCE=$DISTANCE
                  fi
                fi
              fi
            done
            if [ "$BEST_DISTANCE" -eq 99999 ]; then
              BEST_BRANCH=$(echo "$REMOTE_BRANCHES" | head -n 1 || echo "main")
            fi
            BASE_BRANCH=$BEST_BRANCH
          fi
          echo "base_branch=$BASE_BRANCH" >> "$GITHUB_OUTPUT"
          echo "Base branch for $RELEASE_VERSION: $BASE_BRANCH (distance: $BEST_DISTANCE)"
      - name: Notify #product-releases Slack channel
        uses: loft-sh/github-actions/.github/actions/release-notification@v1
        with:
          version: ${{ inputs.release_version }}
          previous_tag: ${{ inputs.previous_tag }}
          changes: "See changelog link below"
          target_repo: "loft-sh/vcluster"
          product: "vCluster"
          base_branch: ${{ steps.get_base_branch.outputs.base_branch }}
          webhook_url: ${{ secrets.SLACK_WEBHOOK_URL_PRODUCT_RELEASES }}
