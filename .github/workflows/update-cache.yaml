name: update-cache

on:
  push:
    branches:
      - "main"

concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: true

jobs:
  build-cache:
    if: github.repository_owner == 'loft-sh' # do not run on forks
    runs-on: ubuntu-latest
    name: Build Cache

    steps:
      - name: Checkout repository
        uses: actions/checkout@v5
        with:
          fetch-depth: 0

      - run: git fetch --force --tags

      - name: Set up Go
        uses: actions/setup-go@v6
        id: cache
        with:
          go-version-file: go.mod

      - name: Setup GoReleaser
        uses: goreleaser/goreleaser-action@v6
        with:
          install-only: true
          version: latest

      - name: Build and save syncer image
        if: steps.cache.outputs.cache-hit != 'true'
        run: |
          set -x
          # Build syncer
          TELEMETRY_PRIVATE_KEY="" goreleaser build --single-target --snapshot --id vcluster --clean --output ./vcluster
          # Build cli
          TELEMETRY_PRIVATE_KEY="" goreleaser build --single-target --snapshot --id vcluster-cli --clean --output ./vcluster
          # Build tests for cache
          go test -mod=vendor -test.v -c ./test/e2e
