name: Release

on:
  release:
    types: [created]

jobs:
  publish:
    if: startsWith(github.ref, 'refs/tags/v') == true
    name: Publish vcluster
    runs-on: ubuntu-22.04

    outputs:
      release_version: ${{ steps.get_version.outputs.release_version }}
      previous_tag: ${{ steps.get_version.outputs.previous_tag }}
      semver_parsed: ${{ steps.semver.outputs.parsed_version }}

    permissions:
      id-token: write # This is the key for OIDC cosign!
      packages: write
      contents: write

    steps:
      - name: Remove default packages
        run: |
          sudo rm -rf /usr/share/dotnet || true
          sudo rm -rf /opt/ghc || true
          sudo rm -rf /usr/local/share/boost || true
          sudo rm -rf /usr/local/lib/android || true
          sudo rm -rf /opt/hostedtoolcache/CodeQL || true
          sudo docker image prune --all --force || true
      - uses: actions/checkout@v5
        with:
          fetch-depth: 0
      - run: git fetch --force --tags
      - name: Set up Go
        uses: actions/setup-go@v6
        with:
          cache: false
          go-version-file: go.mod
      - name: Setup Just
        uses: extractions/setup-just@v2
      - name: Setup Cosgin
        uses: sigstore/cosign-installer@main
        with:
          cosign-release: "v2.2.3"
      - name: Setup Syft
        uses: anchore/sbom-action/download-syft@v0.20.5
      - name: Set up QEMU
        uses: docker/setup-qemu-action@v3
      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3
      - uses: azure/setup-helm@v4
        with:
          version: "v3.0.2"
      - id: get_version
        run: |
          RELEASE_VERSION=$(echo "$GITHUB_REF" | sed -nE 's!refs/tags/!!p')
          echo "release_version=$RELEASE_VERSION" >> "$GITHUB_OUTPUT"
          echo "previous_tag=$(git describe --abbrev=0 --tags "$(git rev-list --tags --skip=1 --max-count=1)")" >> "$GITHUB_OUTPUT"
      - name: Validate semver
        id: semver
        uses: loft-sh/github-actions/.github/actions/semver-validation@semver-validation/v1
        with:
          version: ${{ steps.get_version.outputs.release_version }}
      - name: Login to DockerHub
        uses: docker/login-action@v3
        with:
          username: ${{ secrets.DOCKER_USERNAME }}
          password: ${{ secrets.DOCKER_PASSWORD }}
      - name: Login to ghcr.io
        uses: docker/login-action@v3
        with:
          registry: ghcr.io
          username: ${{ secrets.DOCKER_USERNAME }}
          password: ${{ secrets.GITHUB_TOKEN }}
      - uses: "goreleaser/goreleaser-action@v6"
        with:
          args: release --clean --timeout 60m
          version: "~> v2"
        env:
          GITHUB_TOKEN: ${{ secrets.GH_ACCESS_TOKEN }}
          TELEMETRY_PRIVATE_KEY: ${{ secrets.VCLUSTER_TELEMETRY_PRIVATE_KEY }}
          GORELEASER_CURRENT_TAG: ${{ steps.get_version.outputs.release_version }}
          GORELEASER_PREVIOUS_TAG: ${{ steps.get_version.outputs.previous_tag }}
      - name: Save release assets
        uses: actions/upload-artifact@v4
        with:
          name: release-assets
          path: release/
      - name: Attach assets to release
        uses: FabianKramm/release-asset-action@v1
        with:
          pattern: "release/*"
          github-token: ${{ secrets.GITHUB_TOKEN }}

      - name: Trigger cli-docs workflow
        uses: actions/github-script@v8
        continue-on-error: true
        with:
          github-token: ${{ secrets.GH_ACCESS_TOKEN }}
          script: |
            const version = '${{ steps.get_version.outputs.release_version }}';
            const ref = '${{ github.ref }}';
            // Using repository_dispatch instead of workflow_dispatch
            const response = await github.rest.repos.createDispatchEvent({
              owner: 'loft-sh',
              repo: 'vcluster-docs',
              event_type: 'update-cli-docs',
              client_payload: {
                version,
                ref,
              }
            });
            console.log(response);

  publish-chart:
    if: startsWith(github.ref, 'refs/tags/v') == true
    needs: [publish]
    uses: ./.github/workflows/publish-chart.yaml
    with:
      release_version: ${{ needs.publish.outputs.release_version }}
      ref: ${{ github.ref }}
    secrets: inherit

  # The workflow will only trigger on non-draft releases
  # https://docs.github.com/en/actions/writing-workflows/choosing-when-your-workflow-runs/events-that-trigger-workflows#release
  sync_linear:
    if: ${{ !contains(needs.publish.outputs.semver_parsed, '-next') }}
    needs:
      - publish
      - publish-chart
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v5
      - uses: actions/setup-go@v6
        with:
          go-version-file: go.mod
      - name: Update linear issues
        run: go run . -release-tag="${{ needs.publish.outputs.release_version }}"
        working-directory: hack/linear-sync
        env:
          GITHUB_TOKEN: ${{ secrets.GH_ACCESS_TOKEN }}
          LINEAR_TOKEN: ${{ secrets.LINEAR_TOKEN }}

  notify_release:
    if: ${{ always() }}
    needs:
      - publish
      - publish-chart
      - sync_linear
    uses: ./.github/workflows/notify-release.yaml
    with:
      release_version: ${{ needs.publish.outputs.release_version }}
      previous_tag: ${{ needs.publish.outputs.previous_tag }}
      ref: ${{ github.ref }}
    secrets: inherit
