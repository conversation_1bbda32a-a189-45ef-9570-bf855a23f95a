# Keep the entries sorted and optionally grouped.

* @loft-sh/eng-dev-vcluster-platform

/.github/workflows/backport.yaml @Piotr1215
/.github/workflows/e2e.yaml @FabianKramm
/.github/workflows/e2e.yaml @loft-sh/eng-qa
/.github/workflows/release.yaml @FabianKramm
/.github/workflows/ @Piotr1215 @sydorovdmytro
/.github/CODEOWNERS @FabianKramm

/chart/templates/ @FabianKramm

/cmd/vcluster/ @FabianKramm

/config/ @FabianKramm

/docs/ @loft-sh/Eng-Docs-Admin

/test/ @loft-sh/eng-qa @loft-sh/eng-dev-vcluster-platform

/hack/email/ @zerbitx

/pkg/authentication/ @FabianKramm
/pkg/authorization/ @FabianKramm

/pkg/config/config.go @FabianKramm

/pkg/controllers/k8sdefaultendpoint/ @FabianKramm
/pkg/controllers/register.go @FabianKramm
/pkg/controllers/resources/*/from_host_syncer*.go @hidalgopl
/pkg/controllers/resources/configmaps/ @FabianKramm
/pkg/controllers/resources/endpoints/ @FabianKramm
/pkg/controllers/resources/events/ @FabianKramm
/pkg/controllers/resources/pods/ @FabianKramm
/pkg/controllers/resources/secrets/ @FabianKramm
/pkg/controllers/resources/services/ @FabianKramm

/pkg/k8s/ @FabianKramm
/pkg/mappings/ @FabianKramm
/pkg/platform/ @FabianKramm
/pkg/plugin/ @FabianKramm
/pkg/server/ @FabianKramm
/pkg/setup/ @FabianKramm
/pkg/syncer/ @FabianKramm
/pkg/util/portforward/ @FabianKramm @lizardruss
/pkg/util/servicecidr/ @FabianKramm @lizardruss
/pkg/util/translate/ @FabianKramm

Dockerfile @FabianKramm
Dockerfile.release @FabianKramm
go.mod @FabianKramm

netlify.toml @loft-sh/Eng-Docs-Admin
