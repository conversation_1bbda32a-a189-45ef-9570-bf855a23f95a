# Security Policy
We will disclose fixes for vulnerabilities in the release notes and urge you to upgrade once a new release is published.

**To receive update warnings in the CLI (as part of the terminal output whenever you run a command), you must use an official release binary as published on the [GitHub releases page](https://github.com/loft-sh/vcluster/releases) of this project.**

See the [install instructions for VCluster](https://www.vcluster.com/docs/getting-started/setup) for the recommended methods of downloading an official release binary for your platform. Community maintained release binaries (e.g. `brew` formulary for DevSpace) may **not** contain the version number and will therefore not be able to perform a version check.


## Reporting a Vulnerability
Please report vulnerabilities to: [<EMAIL>](mailto:<EMAIL>)
