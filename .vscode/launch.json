{
    // Use IntelliSense to learn about possible attributes.
    // Hover to view descriptions of existing attributes.
    // For more information, visit: https://go.microsoft.com/fwlink/?linkid=830387
    "version": "0.2.0",
    "configurations": [
      {
        "name": "Debug cli",
        "type": "go",
        "request": "launch",
        "mode": "debug",
        "program": "${workspaceRoot}/cmd/vclusterctl/main.go",
        "cwd": "${workspaceRoot}",
        "args": ["create", "vcluster-1", "-nvcluster-1"]
      },
      {
        "name": "Debug pro cli",
        "type": "go",
        "request": "launch",
        "mode": "debug",
        "program": "${workspaceRoot}/cmd/vclusterctl/main.go",
        "cwd": "${workspaceRoot}",
        "buildFlags": "-tags=pro",
        "args": ["pro", "start"]
      },
        {
          "name": "Debug vcluster (localhost:2346)",
          "type": "go",
          "request": "attach",
          "mode": "remote",
          "debugAdapter": "dlv-dap", // already a default for local debug, it improves e.g. inspection of the maps of interfaces
          "port": 2346,
          "host": "localhost",
          "substitutePath": [
            {
              "from": "${workspaceFolder}",
              "to": "/vcluster-dev",
            },
          ],
          "showLog": true,
          //"trace": "verbose", // use for debugging problems with delve (breakpoints not working, etc.)
        },
		{
            "name": "Launch e2e tests",
            "type": "go",
            "request": "launch",
            "mode": "test",
            "remotePath": "",
            "program": "${workspaceRoot}/e2e/e2e_suite_test.go",
            "env": {
                "VCLUSTER_CLIENT_TIMEOUT": 32, // 32 is the default; increase to high number when debugging vcluster backend server
                //"VCLUSTER_SUFFIX": "vcluster", // suffix is set to "vcluster" when deployed with `devspace run deploy`
            },
            "args": ["-ginkgo.v"], // add "-ginkgo.focus=.*part of test name.*" to run just the test containing said regexp
            "showLog": true,
        }
    ]
}
