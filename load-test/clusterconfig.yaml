apiVersion: eksctl.io/v1alpha5
kind: ClusterConfig
metadata:
  name: simple-cluster
  region: eu-west-3

nodeGroups:
  - name: ng-1
    instanceType: m5.large
    desiredCapacity: 6
    iam:
      withAddonPolicies:
        ebs: true
iam:
  withOIDC: true

addons:
- name: vpc-cni
  attachPolicyARNs:
  - arn:aws:iam::aws:policy/AmazonEKS_CNI_Policy
- name: aws-ebs-csi-driver
  wellKnownPolicies:      # add IAM and service account
    ebsCSIController: true
