apiVersion: v2
name: vcluster
description: vcluster - Virtual Kubernetes Clusters
home: https://vcluster.com
icon: https://static.loft.sh/branding/logos/vcluster/vertical/vcluster_vertical.svg
keywords:
  - developer
  - development
  - sharing
  - share
  - multi-tenancy
  - tenancy
  - cluster
  - space
  - namespace
  - vcluster
  - vclusters
maintainers:
  - name: Loft Labs, Inc.
    email: <EMAIL>
    url: https://twitter.com/loft_sh
sources:
  - https://github.com/loft-sh/vcluster
type: application

version: 0.0.1 # version is auto-generated by release pipeline
