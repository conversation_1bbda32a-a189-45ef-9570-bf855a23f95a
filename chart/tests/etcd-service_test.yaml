suite: External etcd Service
templates:
  - etcd-service.yaml

tests:
  - it: check disabled
    asserts:
      - hasDocuments:
          count: 0

  - it: enable for k8s & defaults
    release:
      name: my-release
      namespace: my-namespace
    set:
      controlPlane:
        backingStore:
          etcd:
            deploy:
              enabled: true
              service:
                annotations:
                  test: test
        distro:
          k8s:
            enabled: true
    asserts:
      - hasDocuments:
          count: 1
      - equal:
          path: metadata.name
          value: my-release-etcd
      - equal:
          path: metadata.namespace
          value: my-namespace
      - equal:
          path: metadata.annotations.test
          value: test
