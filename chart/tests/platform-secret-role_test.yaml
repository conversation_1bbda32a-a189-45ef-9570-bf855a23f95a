suite: Platform Secret Role
templates:
  - platform-rbac.yaml

tests:
  - it: check explicitly disabled
    set:
      external:
        platform:
          apiKey:
            namespace: "some-other-namespace"
            createRBAC: false
    asserts:
      - hasDocuments:
          count: 0

  - it: check disabled on empty namespace
    set:
      external:
        platform:
          apiKey:
            namespace: ""
    asserts:
      - hasDocuments:
          count: 0

  - it: check disabled on implicit same namespace
    set:
      external:
        platform:
          apiKey:
            secretName: "some-other-secret"
    asserts:
      - hasDocuments:
          count: 0

  - it: automatically create role for specific secret for reading & patching
    release:
      namespace: test
    set:
      external:
        platform:
          apiKey:
            secretName: "my-secret-name"
            namespace: "some-other-namespace"
    asserts:
      - hasDocuments:
          count: 2
      - documentIndex: 0
        lengthEqual:
          path: rules
          count: 1
      - documentIndex: 0
        equal:
          path: metadata.name
          value: "vc-RELEASE-NAME-v-test-platform-role"
      - documentIndex: 1
        equal:
          path: metadata.name
          value: "vc-RELEASE-NAME-v-test-platform-role-binding"
      - documentIndex: 0
        contains:
          path: rules
          count: 1
          content:
            apiGroups: [""]
            resources: ["secrets"]
            verbs: ["get"]
            resourceNames: ["my-secret-name"]
      - documentIndex: 1
        contains:
          path: subjects
          count: 1
          content:
            kind: ServiceAccount
            name: vc-RELEASE-NAME
            namespace: test

  - it: automatically create role for default secret for reading & patching
    release:
      namespace: test
    set:
      external:
        platform:
          apiKey:
            namespace: "some-other-namespace"
    asserts:
      - hasDocuments:
          count: 2
      - documentIndex: 0
        lengthEqual:
          path: rules
          count: 1
      - documentIndex: 0
        equal:
          path: metadata.name
          value: "vc-RELEASE-NAME-v-test-platform-role"
      - documentIndex: 1
        equal:
          path: metadata.name
          value: "vc-RELEASE-NAME-v-test-platform-role-binding"
      - documentIndex: 0
        contains:
          path: rules
          count: 1
          content:
            apiGroups: [""]
            resources: ["secrets"]
            verbs: ["get"]
            resourceNames: ["vcluster-platform-api-key"]
      - documentIndex: 1
        contains:
          path: subjects
          count: 1
          content:
            kind: ServiceAccount
            name: vc-RELEASE-NAME
            namespace: test
