suite: ClusterRoleBinding
templates:
  - clusterrole.yaml

tests:
  - it: disable when volume snapshot rules are disabled
    set:
      rbac:
        enableVolumeSnapshotRules:
          enabled: false
    asserts:
      - hasDocuments:
          count: 0

  - it: enabled by default with default rules
    asserts:
      - hasDocuments:
          count: 1
      - contains:
          path: rules
          content:
            apiGroups: [ "" ]
            resources: [ "persistentvolumes" ]
            verbs:
              [ "get", "list" ]
      - contains:
          path: rules
          content:
            apiGroups: [ "snapshot.storage.k8s.io" ]
            resources: [ "volumesnapshotclasses" ]
            verbs:
              [ "get", "list" ]
      - contains:
          path: rules
          content:
            apiGroups: [ "snapshot.storage.k8s.io" ]
            resources: [ "volumesnapshotcontents" ]
            verbs:
              [ "create", "delete", "patch", "update", "get", "list" ]

  - it: force enable
    set:
      rbac:
        clusterRole:
          enabled: true
    asserts:
      - hasDocuments:
          count: 1

  - it: force disable
    set:
      rbac:
        clusterRole:
          enabled: false
          extraRules:
            - apiGroups: [""]
              resources: ["test123"]
              verbs: ["test123"]
          overwriteRules:
            - apiGroups: [""]
              resources: ["test"]
              verbs: ["test"]
    asserts:
      - hasDocuments:
          count: 0

  - it: enable scheduler (k8s distro explicitly enabled)
    set:
      controlPlane:
        distro:
          k8s:
            enabled: true
            scheduler:
              enabled: true
    asserts:
    - hasDocuments:
        count: 1
    - contains:
        path: rules
        content:
          apiGroups: [ "storage.k8s.io" ]
          resources: [ "storageclasses", "csinodes", "csidrivers", "csistoragecapacities" ]
          verbs: [ "get", "watch", "list" ]

  - it: enable scheduler (k8s distro implicitly enabled)
    set:
      controlPlane:
        distro:
          k8s:
            scheduler:
              enabled: true
    asserts:
      - hasDocuments:
          count: 1
      - contains:
          path: rules
          content:
            apiGroups: [ "storage.k8s.io" ]
            resources: [ "storageclasses", "csinodes", "csidrivers", "csistoragecapacities" ]
            verbs: [ "get", "watch", "list" ]

  - it: enable scheduler (deprecated)
    set:
      controlPlane:
        advanced:
          virtualScheduler:
            enabled: true
    asserts:
      - hasDocuments:
          count: 1
      - contains:
          path: rules
          content:
            apiGroups: [ "storage.k8s.io" ]
            resources: [ "storageclasses", "csinodes", "csidrivers", "csistoragecapacities" ]
            verbs: [ "get", "watch", "list" ]

  - it: scheduler not enabled for k3s
    set:
      controlPlane:
        distro:
          k3s:
            enabled: true
          k8s:
            scheduler:
              enabled: true
    asserts:
      - hasDocuments:
          count: 1

  - it: enable hybrid scheduling
    set:
      sync:
        toHost:
          pods:
            hybridScheduling:
              enabled: true
    asserts:
    - hasDocuments:
        count: 1
    - contains:
        path: rules
        content:
          apiGroups: [ "storage.k8s.io" ]
          resources: [ "storageclasses", "csinodes", "csidrivers", "csistoragecapacities" ]
          verbs: [ "get", "watch", "list" ]

  - it: enable csinodes
    set:
      sync:
        fromHost:
          csiNodes:
            enabled: true
    asserts:
      - hasDocuments:
          count: 1
      - contains:
          path: rules
          content:
            apiGroups: [ "storage.k8s.io" ]
            resources: [ "csinodes" ]
            verbs: [ "get", "watch", "list" ]

  - it: enable by multi namespace mode
    set:
      rbac:
        clusterRole:
          enabled: auto
      sync:
        toHost:
          namespaces:
            enabled: true
    asserts:
      - hasDocuments:
          count: 1
      - lengthEqual:
          path: rules
          count: 4
      - contains:
          path: rules
          content:
            apiGroups: [ "" ]
            resources: [ "namespaces", "serviceaccounts"]
            verbs: [ "create", "delete", "patch", "update", "get", "watch", "list" ]

  - it: override rules
    set:
      rbac:
        clusterRole:
          extraRules:
            - apiGroups: [""]
              resources: ["test123"]
              verbs: ["test123"]
          overwriteRules:
            - apiGroups: [""]
              resources: ["test"]
              verbs: ["test"]
    asserts:
      - hasDocuments:
          count: 1
      - lengthEqual:
          path: rules
          count: 1
      - contains:
          path: rules
          content:
            apiGroups: [ "" ]
            resources: [ "test" ]
            verbs: [ "test" ]

  - it: extra rules
    set:
      sync:
        toHost:
          priorityClasses:
            enabled: true
      rbac:
        clusterRole:
          extraRules:
            - apiGroups: [ "" ]
              resources: [ "test123" ]
              verbs: [ "test123" ]
    asserts:
      - hasDocuments:
          count: 1
      - lengthEqual:
          path: rules
          count: 5
      - contains:
          path: rules
          content:
            apiGroups: [ "" ]
            resources: [ "test123" ]
            verbs: [ "test123" ]

  - it: plugin rules
    set:
      plugin:
        myTest:
          rbac:
            clusterRole:
              extraRules:
                - apiGroups: [ "" ]
                  resources: [ "test123" ]
                  verbs: [ "test123" ]
      plugins:
        myTest2:
          rbac:
            clusterRole:
              extraRules:
                - apiGroups: [ "" ]
                  resources: [ "test1234" ]
                  verbs: [ "test1234" ]
    asserts:
      - hasDocuments:
          count: 1
      - lengthEqual:
          path: rules
          count: 5
      - contains:
          path: rules
          content:
            apiGroups: [ "" ]
            resources: [ "test123" ]
            verbs: [ "test123" ]
      - contains:
          path: rules
          content:
            apiGroups: [ "" ]
            resources: [ "test1234" ]
            verbs: [ "test1234" ]

  - it: replicate services
    set:
      networking:
        replicateServices:
          fromHost:
            - from: test
              to: other-test
    asserts:
      - hasDocuments:
          count: 1
      - lengthEqual:
          path: rules
          count: 4
      - contains:
          path: rules
          content:
            apiGroups: [ "" ]
            resources: [ "services", "endpoints" ]
            verbs: [ "get", "watch", "list" ]

  - it: real nodes
    set:
      sync:
        fromHost:
          nodes:
            enabled: true
    asserts:
      - hasDocuments:
          count: 1
      - lengthEqual:
          path: rules
          count: 4
      - contains:
          path: rules
          content:
            apiGroups: [ "" ]
            resources: [ "pods", "nodes", "nodes/status", "nodes/metrics", "nodes/stats", "nodes/proxy" ]
            verbs: [ "get", "watch", "list" ]

  - it: virtual scheduler
    set:
      controlPlane:
        advanced:
          virtualScheduler:
            enabled: true
    asserts:
      - hasDocuments:
          count: 1
      - lengthEqual:
          path: rules
          count: 4
      - contains:
          path: rules
          content:
            apiGroups: ["storage.k8s.io"]
            resources: ["storageclasses", "csinodes", "csidrivers", "csistoragecapacities"]
            verbs: ["get", "watch", "list"]

  - it: legacy pro
    set:
      pro: true
    asserts:
      - hasDocuments:
          count: 1
      - lengthEqual:
          path: rules
          count: 6
      - contains:
          path: rules
          content:
            apiGroups: [ "" ]
            resources: [ "pods", "nodes", "nodes/status", "nodes/metrics", "nodes/stats", "nodes/proxy" ]
            verbs: [ "get", "watch", "list" ]
      - contains:
          path: rules
          content:
            apiGroups: [ "cluster.loft.sh", "storage.loft.sh" ]
            resources: [ "features", "virtualclusters" ]
            verbs: [ "get", "list", "watch" ]
      - contains:
          path: rules
          content:
            apiGroups: ["management.loft.sh"]
            resources: ["virtualclusterinstances"]
            verbs: ["get"]

  - it: metrics proxy
    set:
      integrations:
        metricsServer:
          enabled: true
          nodes: true
    release:
      name: my-release
      namespace: my-namespace
    asserts:
      - hasDocuments:
          count: 1
      - contains:
          path: rules
          content:
            apiGroups: [ "metrics.k8s.io" ]
            resources: [ "nodes" ]
            verbs: [ "get", "list" ]

  - it: externalSecrets
    set:
      integrations:
        externalSecrets:
          enabled: true
          webhook:
            enabled: false
    release:
      name: my-release
      namespace: my-namespace
    asserts:
      - hasDocuments:
          count: 1
      - lengthEqual:
          path: rules
          count: 4
      - contains:
          path: rules
          content:
            apiGroups: ["apiextensions.k8s.io"]
            resources: ["customresourcedefinitions"]
            verbs: ["get", "list", "watch"]
  - it: kubeVirt
    set:
      integrations:
        kubeVirt:
          enabled: true
    release:
      name: my-release
      namespace: my-namespace
    asserts:
      - hasDocuments:
          count: 1
      - lengthEqual:
          path: rules
          count: 5
      - contains:
          path: rules
          content:
            apiGroups: ["apiextensions.k8s.io"]
            resources: ["customresourcedefinitions"]
            verbs: ["get", "list", "watch"]
      - contains:
          path: rules
          content:
            apiGroups: ["admissionregistration.k8s.io"]
            resources: ["validatingwebhookconfigurations", "mutatingwebhookconfigurations"]
            verbs: ["get", "list", "watch"]
  - it: certManger enabled
    set:
      integrations:
        certManager:
          enabled: true
    release:
      name: my-release
      namespace: my-namespace
    asserts:
      - hasDocuments:
          count: 1
      - lengthEqual:
          path: rules
          count: 5
      - contains:
          path: rules
          content:
            apiGroups: ["cert-manager.io"]
            resources: ["clusterissuers"]
            verbs: ["get", "list", "watch"]
  - it: crd sync to host
    set:
      sync:
        toHost:
          customResources:
            test.test-group:
              enabled: true
    release:
      name: my-release
      namespace: my-namespace
    asserts:
      - hasDocuments:
          count: 1
      - lengthEqual:
          path: rules
          count: 4
      - contains:
          path: rules
          content:
            apiGroups: [ "apiextensions.k8s.io" ]
            resources: [ "customresourcedefinitions" ]
            verbs: [ "get", "list", "watch" ]

  - it: crd sync from host
    set:
      sync:
        fromHost:
          customResources:
            test.test-group:
              enabled: true
              scope: Cluster
            test.test-versioned-group/v1alpha1:
              enabled: true
              scope: Cluster
    release:
      name: my-release
      namespace: my-namespace
    asserts:
      - hasDocuments:
          count: 1
      - lengthEqual:
          path: rules
          count: 6
      - contains:
          path: rules
          content:
            apiGroups: [ "test-group" ]
            resources: [ "test" ]
            verbs: [ "get", "list", "watch" ]
      - contains:
          path: rules
          content:
            apiGroups: [ "test-versioned-group" ]
            resources: [ "test" ]
            verbs: [ "get", "list", "watch" ]
      - contains:
          path: rules
          content:
            apiGroups: [ "apiextensions.k8s.io" ]
            resources: [ "customresourcedefinitions" ]
            verbs: [ "get", "list", "watch" ]
  - it: eso clusterstore sync
    set:
      integrations:
        externalSecrets:
          enabled: true
          webhook:
            enabled: true
          sync:
            fromHost:
              clusterStores:
                enabled: true
    release:
      name: my-release
      namespace: my-namespace
    asserts:
      - hasDocuments:
          count: 1
      - contains:
          path: rules
          content:
            apiGroups: ["admissionregistration.k8s.io"]
            resources: ["validatingwebhookconfigurations", "mutatingwebhookconfigurations"]
            verbs: ["get", "list", "watch"]
      - contains:
          path: rules
          content:
            apiGroups: [ "external-secrets.io" ]
            resources: [ "clustersecretstores" ]
            verbs: ["get", "list", "watch"]

  - it: fromHost sync configmaps disabled
    set:
      sync:
        fromHost:
          configMaps:
            enabled: false
    asserts:
      - hasDocuments:
          count: 1

  - it: fromHost sync configmaps enabled with wildcard namespace
    set:
      sync:
        fromHost:
          configMaps:
            enabled: true
            mappings:
              byName:
                "": "my-ns/*"
                my-ns/*: "my-ns-2/*"
    asserts:
      - hasDocuments:
          count: 1
      - lengthEqual:
          path: rules
          count: 6
      - contains:
          path: rules
          content:
            apiGroups: [ "" ]
            resourceNames: ["NAMESPACE", "my-ns"]
            resources: [ "namespaces" ]
            verbs: [ "get", "list", "watch" ]
      - contains:
          path: rules
          content:
            apiGroups: [ "" ]
            resources: [ "configmaps" ]
            verbs: [ "list", "watch" ]
      - contains:
          path: rules
          content:
            apiGroups: [ "" ]
            resources: [ "configmaps" ]
            verbs: [ "get" ]

  - it: fromHost sync configmaps enabled with wildcard name
    set:
      sync:
        fromHost:
          configMaps:
            enabled: true
            mappings:
              byName:
                "my-ns/*": "my-ns-4/*"
                my-ns-2/*: "my-ns-3/*"
    asserts:
      - hasDocuments:
          count: 1
      - lengthEqual:
          path: rules
          count: 6
      - contains:
          path: rules
          content:
            apiGroups: [ "" ]
            resourceNames: ["my-ns", "my-ns-2"]
            resources: [ "namespaces" ]
            verbs: [ "get", "list", "watch" ]
      - contains:
          path: rules
          content:
            apiGroups: [ "" ]
            resources: [ "configmaps" ]
            verbs: [ "list", "watch" ]
      - contains:
          path: rules
          content:
            apiGroups: [ "" ]
            resources: [ "configmaps" ]
            verbs: [ "get" ]

  - it: fromHost sync configmaps enabled without wildcards
    set:
      sync:
        fromHost:
          configMaps:
            enabled: true
            mappings:
              byName:
                "my-ns/my-cm": "my-ns-2/my-cm-2"
                my-ns-3/my-cm-2: "my-ns-4/my-cm4"
    asserts:
      - hasDocuments:
          count: 1
      - lengthEqual:
          path: rules
          count: 6
      - contains:
          path: rules
          content:
            apiGroups: [ "" ]
            resourceNames: [ "my-ns", "my-ns-3" ]
            resources: [ "namespaces" ]
            verbs: [ "get", "list", "watch" ]
      - contains:
          path: rules
          content:
            apiGroups: [ "" ]
            resources: [ "configmaps" ]
            verbs: [ "list", "watch" ]
      - contains:
          path: rules
          content:
            apiGroups: [ "" ]
            resourceNames: [ "my-cm", "my-cm-2" ]
            resources: [ "configmaps" ]
            verbs: [ "get"]

  - it: fromHost sync secrets disabled
    set:
      sync:
        fromHost:
          secrets:
            enabled: false
    asserts:
      - hasDocuments:
          count: 1

  - it: fromHost sync secrets enabled with wildcard namespace
    set:
      sync:
        fromHost:
          secrets:
            enabled: true
            mappings:
              byName:
                "": "my-ns/*"
                my-ns/*: "my-ns-2/*"
    asserts:
      - hasDocuments:
          count: 1
      - lengthEqual:
          path: rules
          count: 6
      - contains:
          path: rules
          content:
            apiGroups: [ "" ]
            resourceNames: ["NAMESPACE", "my-ns"]
            resources: [ "namespaces" ]
            verbs: [ "get", "list", "watch" ]
      - contains:
          path: rules
          content:
            apiGroups: [ "" ]
            resources: [ "secrets" ]
            verbs: [ "list", "watch" ]
      - contains:
          path: rules
          content:
            apiGroups: [ "" ]
            resources: [ "secrets" ]
            verbs: [ "get"]

  - it: fromHost sync secrets enabled with wildcard name
    set:
      sync:
        fromHost:
          secrets:
            enabled: true
            mappings:
              byName:
                "my-ns/*": "my-ns-4/*"
                my-ns-2/*: "my-ns-3/*"
    asserts:
      - hasDocuments:
          count: 1
      - lengthEqual:
          path: rules
          count: 6
      - contains:
          path: rules
          content:
            apiGroups: [ "" ]
            resourceNames: ["my-ns", "my-ns-2"]
            resources: [ "namespaces" ]
            verbs: [ "get", "list", "watch" ]
      - contains:
          path: rules
          content:
            apiGroups: [ "" ]
            resources: [ "secrets" ]
            verbs: [ "list", "watch" ]
      - contains:
          path: rules
          content:
            apiGroups: [ "" ]
            resources: [ "secrets" ]
            verbs: [ "get" ]

  - it: fromHost sync secrets enabled without wildcards
    set:
      sync:
        fromHost:
          secrets:
            enabled: true
            mappings:
              byName:
                "my-ns/my-secret": "my-ns-2/my-secret-2"
                my-ns-3/my-secret-2: "my-ns-4/my-secret-4"
    asserts:
      - hasDocuments:
          count: 1
      - lengthEqual:
          path: rules
          count: 6
      - contains:
          path: rules
          content:
            apiGroups: [ "" ]
            resourceNames: [ "my-ns", "my-ns-3" ]
            resources: [ "namespaces" ]
            verbs: [ "get", "list", "watch" ]
      - contains:
          path: rules
          content:
            apiGroups: [ "" ]
            resources: [ "secrets" ]
            verbs: [ "list", "watch" ]
      - contains:
          path: rules
          content:
            apiGroups: [ "" ]
            resourceNames: [ "my-secret", "my-secret-2" ]
            resources: [ "secrets" ]
            verbs: [ "get" ]
  - it: istio enabled
    set:
      integrations:
        istio:
          enabled: true
    release:
      name: my-release
      namespace: my-namespace
    asserts:
      - hasDocuments:
          count: 1
      - lengthEqual:
          path: rules
          count: 4
      - contains:
          path: rules
          content:
            apiGroups: [ "apiextensions.k8s.io" ]
            resources: [ "customresourcedefinitions" ]
            verbs: [ "get", "list", "watch" ]

  - it: volume snapshot rules
    set:
      rbac:
        enableVolumeSnapshotRules:
          enabled: true
    release:
      name: my-release
      namespace: my-namespace
    asserts:
      - hasDocuments:
          count: 1
      - equal:
          path: kind
          value: ClusterRole
      - contains:
          path: rules
          content:
            apiGroups: [ "" ]
            resources: [ "persistentvolumes" ]
            verbs:
              [ "get", "list" ]
      - contains:
          path: rules
          content:
            apiGroups: [ "snapshot.storage.k8s.io" ]
            resources: [ "volumesnapshotclasses" ]
            verbs:
              [ "get", "list" ]
      - contains:
          path: rules
          content:
            apiGroups: [ "snapshot.storage.k8s.io" ]
            resources: [ "volumesnapshotcontents" ]
            verbs:
              [ "create", "delete", "patch", "update", "get", "list" ]
