suite: Workload ServiceAccount
templates:
  - workload-serviceaccount.yaml

tests:
  - it: should not create service account
    set:
      controlPlane:
        advanced:
          workloadServiceAccount:
            enabled: false
    asserts:
      - hasDocuments:
          count: 0

  - it: should create service account
    release:
      name: my-release
      namespace: my-namespace
    asserts:
      - hasDocuments:
          count: 1
      - equal:
          path: metadata.name
          value: vc-workload-my-release
      - equal:
          path: metadata.namespace
          value: my-namespace

  - it: should create service account with name
    set:
      controlPlane:
        advanced:
          workloadServiceAccount:
            name: test
    asserts:
      - hasDocuments:
          count: 1
      - equal:
          path: metadata.name
          value: test

  - it: should create image pull secrets
    set:
      controlPlane:
        advanced:
          serviceAccount:
            imagePullSecrets:
              - name: test1
          workloadServiceAccount:
            imagePullSecrets:
              - name: test2
    asserts:
      - hasDocuments:
          count: 1
      - lengthEqual:
          path: imagePullSecrets
          count: 2
      - equal:
          path: imagePullSecrets[0].name
          value: test1
      - equal:
          path: imagePullSecrets[1].name
          value: test2
