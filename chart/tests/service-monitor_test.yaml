suite: ServiceMonitor
templates:
  - service-monitor.yaml

tests:
  - it: should not create service monitor by default
    asserts:
      - hasDocuments:
          count: 0

  - it: check defaults
    release:
      name: my-release
      namespace: my-namespace
    set:
      controlPlane:
        serviceMonitor:
          enabled: true
    asserts:
      - hasDocuments:
          count: 1
      - equal:
          path: metadata.name
          value: vc-my-release
      - equal:
          path: metadata.namespace
          value: my-namespace
      - equal:
          path: spec.selector.matchLabels.app
          value: vcluster
      - lengthEqual:
          path: spec.endpoints
          count: 2

  - it: check defaults k3s
    release:
      name: my-release
      namespace: my-namespace
    set:
      controlPlane:
        distro:
          k3s:
            enabled: true
        serviceMonitor:
          enabled: true
    asserts:
      - hasDocuments:
          count: 1
      - equal:
          path: metadata.name
          value: vc-my-release
      - equal:
          path: metadata.namespace
          value: my-namespace
      - equal:
          path: spec.selector.matchLabels.app
          value: vcluster
      - lengthEqual:
          path: spec.endpoints
          count: 1

  - it: override release label
    release:
      name: my-release
      namespace: my-namespace
    set:
      controlPlane:
        serviceMonitor:
          enabled: true
          labels:
            release: kube-prometheus-stack
    asserts:
      - hasDocuments:
          count: 1
      - equal:
          path: metadata.labels.release
          value: kube-prometheus-stack

  - it: check virtual scheduler
    release:
      name: my-release
      namespace: my-namespace
    set:
      controlPlane:
        distro:
          k8s:
            enabled: true
            scheduler:
              enabled: true
        serviceMonitor:
          enabled: true
    asserts:
    - hasDocuments:
        count: 1
    - lengthEqual:
        path: spec.endpoints
        count: 3

  - it: check virtual scheduler (deprecated)
    release:
      name: my-release
      namespace: my-namespace
    set:
      controlPlane:
        advanced:
          virtualScheduler:
            enabled: true
        serviceMonitor:
          enabled: true
    asserts:
      - hasDocuments:
          count: 1
      - lengthEqual:
          path: spec.endpoints
          count: 3
