suite: NetworkPolicy
templates:
  - networkpolicy.yaml

tests:
  - it: should not create network policy by default
    asserts:
      - hasDocuments:
          count: 0

  - it: check defaults
    release:
      name: my-release
      namespace: my-namespace
    set:
      policies:
        networkPolicy:
          enabled: true
    asserts:
      - hasDocuments:
          count: 2
      - documentIndex: 0
        equal:
          path: metadata.name
          value: vc-work-my-release
      - documentIndex: 0
        equal:
          path: spec.egress[2].to[1].ipBlock.cidr
          value: 0.0.0.0/0
      - documentIndex: 1
        equal:
          path: metadata.name
          value: vc-cp-my-release
      - documentIndex: 0
        equal:
          path: metadata.namespace
          value: my-namespace
      - documentIndex: 1
        equal:
          path: metadata.namespace
          value: my-namespace
      - documentIndex: 0
        lengthEqual:
          path: spec.egress
          count: 3
      - documentIndex: 1
        lengthEqual:
          path: spec.egress
          count: 2

  - it: check extra control plane rules
    release:
      name: my-release
      namespace: my-namespace
    set:
      policies:
        networkPolicy:
          enabled: true
          extraControlPlaneRules:
            - ports:
              - port: 443
              - port: 8443
              - port: 6443
    asserts:
      - hasDocuments:
          count: 2
      - documentIndex: 0
        lengthEqual:
          path: spec.egress
          count: 3
      - documentIndex: 1
        lengthEqual:
          path: spec.egress
          count: 3
      - documentIndex: 1
        equal:
          path: spec.egress[2].ports[0].port
          value: 443
      - documentIndex: 1
        equal:
          path: spec.egress[2].ports[1].port
          value: 8443
      - documentIndex: 1
        equal:
          path: spec.egress[2].ports[2].port
          value: 6443

  - it: check extra workload rules
    release:
      name: my-release
      namespace: my-namespace
    set:
      policies:
        networkPolicy:
          enabled: true
          extraWorkloadRules:
            - ports:
              - port: 443
              - port: 8443
              - port: 6443
    asserts:
      - hasDocuments:
          count: 2
      - documentIndex: 0
        lengthEqual:
          path: spec.egress
          count: 4
      - documentIndex: 1
        lengthEqual:
          path: spec.egress
          count: 2
      - documentIndex: 0
        equal:
          path: spec.egress[3].ports[0].port
          value: 443
      - documentIndex: 0
        equal:
          path: spec.egress[3].ports[1].port
          value: 8443
      - documentIndex: 0
        equal:
          path: spec.egress[3].ports[2].port
          value: 6443
