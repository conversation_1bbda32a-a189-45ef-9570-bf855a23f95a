suite: ControlPlane Service
templates:
  - service.yaml

tests:
  - it: should not create service
    set:
      controlPlane:
        service:
          enabled: false
    asserts:
      - hasDocuments:
          count: 0

  - it: should not create kubelet port
    set:
      networking:
        advanced:
          proxyKubelets:
            byHostname: false
    asserts:
      - hasDocuments:
          count: 1
      - lengthEqual:
          path: spec.ports
          count: 1
      - contains:
          path: spec.ports
          content:
            name: https
            nodePort: 0
            targetPort: 8443
            protocol: TCP
            port: 443

  - it: should not create kubelet port 2
    set:
      controlPlane:
        service:
          spec:
            type: <PERSON>adBalancer
    asserts:
      - hasDocuments:
          count: 1
      - lengthEqual:
          path: spec.ports
          count: 1
      - contains:
          path: spec.ports
          content:
            name: https
            nodePort: 0
            targetPort: 8443
            protocol: TCP
            port: 443

  - it: should create kubelet port
    asserts:
      - hasDocuments:
          count: 1
      - lengthEqual:
          path: spec.ports
          count: 2
      - contains:
          path: spec.ports
          content:
            name: kubelet
            nodePort: 0
            targetPort: 8443
            protocol: TCP
            port: 10250

  - it: service defaults
    release:
      name: my-release
      namespace: my-namespace
    asserts:
      - hasDocuments:
          count: 1
      - equal:
          path: metadata.name
          value: my-release
      - equal:
          path: metadata.namespace
          value: my-namespace
      - equal:
          path: spec.type
          value: ClusterIP
      - equal:
          path: spec.selector.app
          value: vcluster
      - lengthEqual:
          path: spec.ports
          count: 2

  - it: konnectivity
    release:
      name: my-release
      namespace: my-namespace
    set:
      privateNodes:
        enabled: true
    asserts:
      - hasDocuments:
          count: 1
      - lengthEqual:
          path: spec.ports
          count: 3
      - contains:
          path: spec.ports
          content:
            name: konnectivity
            protocol: TCP
            port: 8132
