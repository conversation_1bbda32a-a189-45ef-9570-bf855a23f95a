suite: LimitRange
templates:
  - limitrange.yaml

tests:
  - it: should not create limit range by default
    asserts:
      - hasDocuments:
          count: 0

  - it: check defaults
    release:
      name: my-release
      namespace: my-namespace
    set:
      policies:
        limitRange:
          enabled: true
    asserts:
      - hasDocuments:
          count: 1
      - equal:
          path: metadata.name
          value: vc-my-release
      - equal:
          path: metadata.namespace
          value: my-namespace
      - lengthEqual:
          path: spec.limits
          count: 1

  - it: check enabled
    release:
      name: my-release
      namespace: my-namespace
    set:
      policies:
        resourceQuota:
          enabled: true
    asserts:
      - hasDocuments:
          count: 1

  - it: check disabled
    release:
      name: my-release
      namespace: my-namespace
    set:
      policies:
        resourceQuota:
          enabled: true
        limitRange:
          enabled: false
    asserts:
      - hasDocuments:
          count: 0

  - it: check disabled both false
    release:
      name: my-release
      namespace: my-namespace
    set:
      policies:
        resourceQuota:
          enabled: false
        limitRange:
          enabled: false
    asserts:
      - hasDocuments:
          count: 0

  - it: check disabled both false
    release:
      name: my-release
      namespace: my-namespace
    set:
      policies:
        limitRange:
          enabled: true
          min:
            cpu: 1
          max:
            memory: 256Mi
    asserts:
      - hasDocuments:
          count: 1
      - equal:
          path: spec.limits[0].min.cpu
          value: "1"
      - equal:
          path: spec.limits[0].max.memory
          value: "256Mi"


