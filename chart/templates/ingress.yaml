{{- if .Values.controlPlane.ingress.enabled }}
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  {{- $annotations := merge dict .Values.controlPlane.ingress.annotations .Values.controlPlane.advanced.globalMetadata.annotations }}
  {{- if $annotations }}
  annotations:
  {{- toYaml $annotations | nindent 4 }}
  {{- end }}
  name: {{ .Release.Name }}
  namespace: {{ .Release.Namespace }}
  labels:
    app: vcluster
    chart: "{{ include "vcluster.version.label"  $ }}"
    release: "{{ .Release.Name }}"
    heritage: "{{ .Release.Service }}"
  {{- if .Values.controlPlane.ingress.labels }}
{{ toYaml .Values.controlPlane.ingress.labels | indent 4 }}
  {{- end }}
spec:
  {{- with .Values.controlPlane.ingress.spec }}
  {{- tpl (toYaml .) $ | nindent 2 }}
  {{- end }}
  {{- if not .Values.controlPlane.ingress.spec.rules }}
  rules:
    - host: {{ .Values.controlPlane.ingress.host | quote }}
      http:
       paths:
        - backend:
            service:
              name: {{ .Release.Name }}
              port:
                name: https
          path: /
          pathType: {{ .Values.controlPlane.ingress.pathType }}
  {{- end }}
{{- end }}
