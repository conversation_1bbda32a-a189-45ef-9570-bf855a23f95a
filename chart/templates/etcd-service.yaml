{{- if or .Values.controlPlane.backingStore.etcd.deploy.enabled (include "vcluster.etcd.embedded.migrate" .) }}
{{- if .Values.controlPlane.backingStore.etcd.deploy.service.enabled }}
apiVersion: v1
kind: Service
metadata:
  name: {{ .Release.Name }}-etcd
  namespace: {{ .Release.Namespace }}
  labels:
    app: vcluster-etcd
    chart: "{{ include "vcluster.version.label"  $ }}"
    release: "{{ .Release.Name }}"
    heritage: "{{ .Release.Service }}"
  {{- $annotations := merge dict .Values.controlPlane.advanced.globalMetadata.annotations .Values.controlPlane.backingStore.etcd.deploy.service.annotations }}
  {{- if $annotations }}
  annotations:
{{ toYaml $annotations | indent 4 }}
  {{- end }}
spec:
  type: ClusterIP
  ports:
    - name: etcd
      port: 2379
      targetPort: 2379
      protocol: TCP
    - name: peer
      port: 2380
      targetPort: 2380
      protocol: TCP
  selector:
    app: vcluster-etcd
    release: {{ .Release.Name }}
{{- end }}
{{- end }}
