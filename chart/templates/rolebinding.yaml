{{- if .Values.rbac.role.enabled }}
{{- if .Values.sync.toHost.namespaces.enabled}}
kind: ClusterRoleBinding
{{- else -}}
kind: RoleBinding
{{- end }}
apiVersion: rbac.authorization.k8s.io/v1
metadata:
{{- if .Values.sync.toHost.namespaces.enabled }}
  name: {{ template "vcluster.clusterRoleNameMultinamespace" . }}
{{- else }}
  name: vc-{{ .Release.Name }}
  namespace: {{ .Release.Namespace }}
{{- end }}
  labels:
    app: vcluster
    chart: "{{ include "vcluster.version.label"  $ }}"
    release: "{{ .Release.Name }}"
    heritage: "{{ .Release.Service }}"
  {{- if .Values.controlPlane.advanced.globalMetadata.annotations }}
  annotations:
{{ toYaml .Values.controlPlane.advanced.globalMetadata.annotations | indent 4 }}
  {{- end }}
subjects:
  - kind: ServiceAccount
    {{- if .Values.controlPlane.advanced.serviceAccount.name }}
    name: {{ .Values.controlPlane.advanced.serviceAccount.name }}
    {{- else }}
    name: vc-{{ .Release.Name }}
    {{- end }}
    namespace: {{ .Release.Namespace }}
roleRef:
{{- if .Values.sync.toHost.namespaces.enabled }}
  kind: ClusterRole
  name: {{ template "vcluster.clusterRoleNameMultinamespace" . }}
{{- else }}
  kind: Role
  name: vc-{{ .Release.Name }}
{{- end }}
  apiGroup: rbac.authorization.k8s.io
{{- end }}
