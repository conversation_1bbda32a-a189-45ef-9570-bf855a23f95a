{{- if .Values.controlPlane.advanced.workloadServiceAccount.enabled }}
apiVersion: v1
kind: ServiceAccount
metadata:
  {{- if .Values.controlPlane.advanced.workloadServiceAccount.name }}
  name: {{ .Values.controlPlane.advanced.workloadServiceAccount.name | quote }}
  {{- else }}
  name: vc-workload-{{ .Release.Name }}
  {{- end }}
  namespace: {{ .Release.Namespace }}
  labels:
    app: vcluster
    chart: "{{ include "vcluster.version.label"  $ }}"
    release: "{{ .Release.Name }}"
    heritage: "{{ .Release.Service }}"
  {{- if .Values.controlPlane.advanced.workloadServiceAccount.labels }}
{{ toYaml .Values.controlPlane.advanced.workloadServiceAccount.labels | indent 4 }}
  {{- end }}
  {{- $annotations := merge dict .Values.controlPlane.advanced.workloadServiceAccount.annotations .Values.controlPlane.advanced.globalMetadata.annotations }}
  {{- if $annotations }}
  annotations:
{{- toYaml $annotations | nindent 4 }}
  {{- end }}
{{- $pullSecrets := concat .Values.controlPlane.advanced.serviceAccount.imagePullSecrets .Values.controlPlane.advanced.workloadServiceAccount.imagePullSecrets }}
{{- if $pullSecrets }}
imagePullSecrets:
{{ toYaml $pullSecrets | indent 2 }}
{{- end }}
{{- end }}
