{{- if or (eq (toString .Values.policies.limitRange.enabled) "true") (eq (toString .Values.policies.resourceQuota.enabled) "true") }}
{{- if not (eq (toString .Values.policies.limitRange.enabled) "false") }}
apiVersion: v1
kind: LimitRange
metadata:
  name: vc-{{ .Release.Name }}
  namespace: {{ .Release.Namespace }}
  labels:
    app: vcluster
    chart: "{{ include "vcluster.version.label"  $ }}"
    release: "{{ .Release.Name }}"
    heritage: "{{ .Release.Service }}"
    {{- if .Values.policies.limitRange.labels }}
{{ toYaml .Values.policies.limitRange.labels | indent 4 }}
    {{- end }}
  {{- $annotations := merge dict .Values.controlPlane.advanced.globalMetadata.annotations .Values.policies.limitRange.annotations }}
  {{- if $annotations }}
  annotations:
{{ toYaml $annotations | indent 4 }}
  {{- end }}
spec:
  limits:
  - default:
      {{- range $key, $val := .Values.policies.limitRange.default }}
      {{ $key }}: {{ $val | quote }}
      {{- end }}
    defaultRequest:
      {{- range $key, $val := .Values.policies.limitRange.defaultRequest }}
      {{ $key }}: {{ $val | quote }}
      {{- end }}
    {{- if .Values.policies.limitRange.min }}
    min:
      {{- range $key, $val := .Values.policies.limitRange.min }}
      {{ $key }}: {{ $val | quote }}
      {{- end }}
    {{- end }}
    {{- if .Values.policies.limitRange.max }}
    max:
      {{- range $key, $val := .Values.policies.limitRange.max }}
      {{ $key }}: {{ $val | quote }}
      {{- end }}
    {{- end }}
    type: Container
{{- end }}
{{- end }}
