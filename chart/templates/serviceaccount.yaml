{{- if .Values.controlPlane.advanced.serviceAccount.enabled }}
apiVersion: v1
kind: ServiceAccount
metadata:
  {{- if .Values.controlPlane.advanced.serviceAccount.name }}
  name: {{ .Values.controlPlane.advanced.serviceAccount.name | quote }}
  {{- else }}
  name: vc-{{ .Release.Name }}
  {{- end }}
  namespace: {{ .Release.Namespace }}
  labels:
    app: vcluster
    chart: "{{ include "vcluster.version.label"  $ }}"
    release: "{{ .Release.Name }}"
    heritage: "{{ .Release.Service }}"
  {{- if .Values.controlPlane.advanced.serviceAccount.labels }}
{{ toYaml .Values.controlPlane.advanced.serviceAccount.labels | indent 4 }}
  {{- end }}
  {{- $annotations := merge dict .Values.controlPlane.advanced.serviceAccount.annotations .Values.controlPlane.advanced.globalMetadata.annotations }}
  {{- if $annotations }}
  annotations:
{{- toYaml $annotations | nindent 4 }}
  {{- end }}
{{- if .Values.controlPlane.advanced.serviceAccount.imagePullSecrets }}
imagePullSecrets:
{{ toYaml .Values.controlPlane.advanced.serviceAccount.imagePullSecrets | indent 2 }}
{{- end }}
{{- end }}
