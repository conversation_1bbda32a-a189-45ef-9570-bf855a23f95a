apiVersion: v1
kind: Secret
metadata:
  name: "vc-config-{{ .Release.Name }}"
  namespace: {{ .Release.Namespace }}
  labels:
    app: vcluster
    chart: "{{ include "vcluster.version.label"  $ }}"
    release: "{{ .Release.Name }}"
    heritage: "{{ .Release.Service }}"
  {{- if .Values.controlPlane.advanced.globalMetadata.annotations }}
  annotations:
{{ toYaml .Values.controlPlane.advanced.globalMetadata.annotations | indent 4 }}
  {{- end }}
type: Opaque
data:
  config.yaml: {{ .Values | toYaml | b64enc | quote }}
