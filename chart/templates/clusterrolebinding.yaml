{{- if (include "vcluster.createClusterRole" . ) -}}
kind: ClusterRoleBinding
apiVersion: rbac.authorization.k8s.io/v1
metadata:
  name: {{ template "vcluster.clusterRoleName" . }}
  labels:
    app: vcluster
    chart: "{{ include "vcluster.version.label"  $ }}"
    release: "{{ .Release.Name }}"
    heritage: "{{ .Release.Service }}"
  {{- if .Values.controlPlane.advanced.globalMetadata.annotations }}
  annotations:
{{ toYaml .Values.controlPlane.advanced.globalMetadata.annotations | indent 4 }}
  {{- end }}
subjects:
  - kind: ServiceAccount
    {{- if .Values.controlPlane.advanced.serviceAccount.name }}
    name: {{ .Values.controlPlane.advanced.serviceAccount.name }}
    {{- else }}
    name: vc-{{ .Release.Name }}
    {{- end }}
    namespace: {{ .Release.Namespace }}
roleRef:
  kind: ClusterRole
  name: {{ template "vcluster.clusterRoleName" . }}
  apiGroup: rbac.authorization.k8s.io
{{- end }}
