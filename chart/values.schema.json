{"$schema": "https://json-schema.org/draft/2020-12/schema", "$id": "https://vcluster.com/schemas/config", "$defs": {"APIService": {"properties": {"service": {"$ref": "#/$defs/APIServiceService", "description": "Service is a reference to the service for the API server."}}, "additionalProperties": false, "type": "object", "description": "APIService holds configuration related to the api server"}, "APIServiceService": {"properties": {"name": {"type": "string", "description": "Name is the name of the host service of the apiservice."}, "namespace": {"type": "string", "description": "Namespace is the name of the host service of the apiservice."}, "port": {"type": "integer", "description": "Port is the target port on the host service to connect to."}}, "additionalProperties": false, "type": "object", "description": "APIServiceService holds the service name and namespace of the host apiservice."}, "AutoSleepExclusion": {"properties": {"selector": {"$ref": "#/$defs/LabelSelector"}}, "additionalProperties": false, "type": "object", "description": "AutoSleepExclusion holds conifiguration for excluding workloads from sleeping by label(s)"}, "AutoUpgrade": {"properties": {"enabled": {"type": "boolean", "description": "Enabled defines if auto upgrade should be enabled."}, "image": {"type": "string", "description": "Image is the image for the auto upgrade pod started by vCluster. If empty defaults to the controlPlane.statefulSet.image."}, "imagePullPolicy": {"type": "string", "description": "ImagePullPolicy is the policy how to pull the image."}, "nodeSelector": {"additionalProperties": {"type": "string"}, "type": "object", "description": "NodeSelector is the node selector for the auto upgrade. If empty will select all worker nodes."}, "binariesPath": {"type": "string", "description": "BinariesPath is the base path for the kubeadm binaries. Defaults to /usr/local/bin"}, "cniBinariesPath": {"type": "string", "description": "CNIBinariesPath is the base path for the CNI binaries. Defaults to /opt/cni/bin"}, "concurrency": {"type": "integer", "description": "Concurrency is the number of nodes that can be upgraded at the same time."}}, "additionalProperties": false, "type": "object"}, "AutoWakeup": {"properties": {"schedule": {"type": "string"}}, "additionalProperties": false, "type": "object", "description": "AutoWakeup holds the cron schedule to wake workloads automatically"}, "BackingStore": {"properties": {"etcd": {"$ref": "#/$defs/Etcd", "description": "Etcd defines that etcd should be used as the backend for the virtual cluster"}, "database": {"$ref": "#/$defs/Database", "description": "Database defines that a database backend should be used as the backend for the virtual cluster. This uses a project called kine under the hood which is a shim for bridging Kubernetes and relational databases."}}, "additionalProperties": false, "type": "object"}, "CNI": {"properties": {"flannel": {"$ref": "#/$defs/CNIFlannel", "description": "Flannel holds dedicated Flannel configuration."}}, "additionalProperties": false, "type": "object"}, "CNIFlannel": {"properties": {"enabled": {"type": "boolean", "description": "Enabled defines if Flannel should be enabled."}, "image": {"type": "string", "description": "Image is the image for Flannel main container."}, "initImage": {"type": "string", "description": "InitImage is the image for Flannel init container."}, "imagePullPolicy": {"type": "string", "description": "ImagePullPolicy is the policy how to pull the image."}}, "additionalProperties": false, "type": "object"}, "CentralAdmission": {"properties": {"validatingWebhooks": {"items": {"$ref": "#/$defs/ValidatingWebhookConfiguration"}, "type": "array", "description": "ValidatingWebhooks are validating webhooks that should be enforced in the virtual cluster"}, "mutatingWebhooks": {"items": {"$ref": "#/$defs/MutatingWebhookConfiguration"}, "type": "array", "description": "MutatingWebhooks are mutating webhooks that should be enforced in the virtual cluster"}}, "additionalProperties": false, "type": "object"}, "CertManager": {"properties": {"enabled": {"type": "boolean", "description": "Enabled defines if this option should be enabled."}, "sync": {"$ref": "#/$defs/CertManagerSync", "description": "Sync contains advanced configuration for syncing cert-manager resources."}}, "additionalProperties": false, "type": "object", "description": "CertManager reuses a host cert-manager and makes its CRDs from it available inside the vCluster"}, "CertManagerSync": {"properties": {"toHost": {"$ref": "#/$defs/CertManagerSyncToHost"}, "fromHost": {"$ref": "#/$defs/CertManagerSyncFromHost"}}, "additionalProperties": false, "type": "object"}, "CertManagerSyncFromHost": {"properties": {"clusterIssuers": {"$ref": "#/$defs/ClusterIssuersSyncConfig", "description": "ClusterIssuers defines if (and which) cluster issuers should get synced from the host cluster to the virtual cluster."}}, "additionalProperties": false, "type": "object"}, "CertManagerSyncToHost": {"properties": {"certificates": {"$ref": "#/$defs/EnableSwitch", "description": "Certificates defines if certificates should get synced from the virtual cluster to the host cluster."}, "issuers": {"$ref": "#/$defs/EnableSwitch", "description": "Issuers defines if issuers should get synced from the virtual cluster to the host cluster."}}, "additionalProperties": false, "type": "object"}, "CloudControllerManager": {"properties": {"enabled": {"type": "boolean", "description": "Enabled defines if the embedded cloud controller manager should be enabled. This defaults to true, but can be disabled if you want to use\nan external cloud controller manager such as AWS or GCP. The cloud controller manager is responsible for setting the node's ip addresses as well\nas the provider id for the node and other node metadata."}}, "additionalProperties": false, "type": "object"}, "ClusterIssuersSyncConfig": {"properties": {"enabled": {"type": "boolean", "description": "Enabled defines if this option should be enabled."}, "selector": {"$ref": "#/$defs/LabelSelector", "description": "Selector defines what cluster issuers should be imported."}}, "additionalProperties": false, "type": "object"}, "ContainerdJoin": {"properties": {"enabled": {"type": "boolean", "description": "Enabled defines if containerd should be installed and configured by vCluster."}, "registry": {"$ref": "#/$defs/ContainerdRegistry", "description": "Registry holds configuration for how containerd should be configured to use a registries."}, "pauseImage": {"type": "string", "description": "PauseImage is the image for the pause container."}}, "additionalProperties": false, "type": "object"}, "ContainerdMirror": {"properties": {"server": {"type": "string", "description": "Server is the fallback server to use for the containerd registry mirror. E.g. https://registry-1.docker.io. See https://github.com/containerd/containerd/blob/main/docs/hosts.md for more details."}, "caCert": {"items": {"type": "string"}, "type": "array", "description": "CACert are paths to CA certificates to use for the containerd registry mirror."}, "skipVerify": {"type": "boolean", "description": "SkipVerify is a boolean to skip the certificate verification for the containerd registry mirror and allows http connections."}, "capabilities": {"items": {"type": "string"}, "type": "array", "description": "Capabilities is a list of capabilities to enable for the containerd registry mirror. If empty, will use pull and resolve capabilities."}, "overridePath": {"type": "boolean", "description": "OverridePath is a boolean to override the path for the containerd registry mirror."}, "hosts": {"items": {"$ref": "#/$defs/ContainerdMirrorHost"}, "type": "array", "description": "Hosts holds configuration for the containerd registry mirror hosts. See https://github.com/containerd/containerd/blob/main/docs/hosts.md for more details."}}, "additionalProperties": false, "type": "object"}, "ContainerdMirrorHost": {"properties": {"server": {"type": "string", "description": "Server is the server to use for the containerd registry mirror host. E.g. http://**************:5000."}, "caCert": {"items": {"type": "string"}, "type": "array", "description": "CACert are paths to CA certificates to use for the containerd registry mirror host."}, "skipVerify": {"type": "boolean", "description": "SkipVerify is a boolean to skip the certificate verification for the containerd registry mirror and allows http connections."}, "capabilities": {"items": {"type": "string"}, "type": "array", "description": "Capabilities is a list of capabilities to enable for the containerd registry mirror. If empty, will use pull and resolve capabilities."}, "overridePath": {"type": "boolean", "description": "OverridePath is a boolean to override the path for the containerd registry mirror."}}, "additionalProperties": false, "type": "object"}, "ContainerdRegistry": {"properties": {"configPath": {"type": "string", "description": "ConfigPath is the path to the containerd registry config."}, "mirrors": {"additionalProperties": {"$ref": "#/$defs/ContainerdMirror"}, "type": "object", "description": "Mirrors holds configuration for the containerd registry mirrors. E.g. myregistry.io:5000 or docker.io. See https://github.com/containerd/containerd/blob/main/docs/hosts.md for more details."}, "auth": {"additionalProperties": {"$ref": "#/$defs/ContainerdRegistryAuth"}, "type": "object", "description": "Auth holds configuration for the containerd registry auth. See https://github.com/containerd/containerd/blob/main/docs/cri/registry.md#configure-registry-credentials for more details."}}, "additionalProperties": false, "type": "object"}, "ContainerdRegistryAuth": {"properties": {"username": {"type": "string", "description": "Username is the username for the containerd registry."}, "password": {"type": "string", "description": "Password is the password for the containerd registry."}, "identityToken": {"type": "string", "description": "IdentityToken is the token for the containerd registry."}, "auth": {"type": "string", "description": "Auth is the auth config for the containerd registry."}}, "additionalProperties": false, "type": "object"}, "ControlPlane": {"properties": {"endpoint": {"type": "string", "description": "Endpoint is the endpoint of the virtual cluster. This is used to connect to the virtual cluster."}, "distro": {"$ref": "#/$defs/Distro", "description": "Distro holds virtual cluster related distro options. A distro cannot be changed after vCluster is deployed."}, "standalone": {"$ref": "#/$defs/Standalone", "description": "Standalone holds configuration for standalone mode. Standalone mode is set automatically when no container is detected and\nalso implies privateNodes.enabled."}, "backingStore": {"$ref": "#/$defs/BackingStore", "description": "BackingStore defines which backing store to use for virtual cluster. If not defined will use embedded database as a default backing store."}, "coredns": {"$ref": "#/$defs/CoreDNS", "description": "CoreDNS defines everything related to the coredns that is deployed and used within the vCluster."}, "proxy": {"$ref": "#/$defs/ControlPlaneProxy", "description": "Proxy defines options for the virtual cluster control plane proxy that is used to do authentication and intercept requests."}, "hostPathMapper": {"$ref": "#/$defs/HostPathMapper", "description": "HostPathMapper defines if vCluster should rewrite host paths.", "pro": true}, "ingress": {"$ref": "#/$defs/ControlPlaneIngress", "description": "Ingress defines options for vCluster ingress deployed by Helm."}, "service": {"$ref": "#/$defs/ControlPlaneService", "description": "Service defines options for vCluster service deployed by Helm."}, "statefulSet": {"$ref": "#/$defs/ControlPlaneStatefulSet", "description": "StatefulSet defines options for vCluster statefulSet deployed by Helm."}, "serviceMonitor": {"$ref": "#/$defs/ServiceMonitor", "description": "ServiceMonitor can be used to automatically create a service monitor for vCluster deployment itself."}, "advanced": {"$ref": "#/$defs/ControlPlaneAdvanced", "description": "Advanced holds additional configuration for the vCluster control plane."}}, "additionalProperties": false, "type": "object"}, "ControlPlaneAdvanced": {"properties": {"defaultImageRegistry": {"type": "string", "description": "DefaultImageRegistry will be used as a prefix for all internal images deployed by vCluster or Helm. This makes it easy to\nupload all required vCluster images to a single private repository and set this value. Workload images are not affected by this."}, "virtualScheduler": {"$ref": "#/$defs/EnableSwitch", "description": "VirtualScheduler defines if a scheduler should be used within the virtual cluster or the scheduling decision for workloads will be made by the host cluster.\nDeprecated: Use ControlPlane.Distro.K8S.Scheduler instead."}, "serviceAccount": {"$ref": "#/$defs/ControlPlaneServiceAccount", "description": "ServiceAccount specifies options for the vCluster control plane service account."}, "workloadServiceAccount": {"$ref": "#/$defs/ControlPlaneWorkloadServiceAccount", "description": "WorkloadServiceAccount specifies options for the service account that will be used for the workloads that run within the virtual cluster."}, "headlessService": {"$ref": "#/$defs/ControlPlaneHeadlessService", "description": "HeadlessService specifies options for the headless service used for the vCluster StatefulSet."}, "konnectivity": {"$ref": "#/$defs/Konnectivity", "description": "Konnectivity holds dedicated konnectivity configuration. This is only available when privateNodes.enabled is true."}, "registry": {"$ref": "#/$defs/Registry", "description": "Registry allows enabling an embedded docker image registry in vCluster. This is useful for air-gapped environments or when you don't have a public registry available to distribute images."}, "cloudControllerManager": {"$ref": "#/$defs/CloudControllerManager", "description": "CloudControllerManager holds configuration for the embedded cloud controller manager. This is only available when private nodes are enabled.\nThe cloud controller manager is responsible for setting the node's ip addresses as well as the provider id for the node and other node metadata."}, "globalMetadata": {"$ref": "#/$defs/ControlPlaneGlobalMetadata", "description": "GlobalMetadata is metadata that will be added to all resources deployed by Helm."}}, "additionalProperties": false, "type": "object"}, "ControlPlaneGlobalMetadata": {"properties": {"annotations": {"additionalProperties": {"type": "string"}, "type": "object", "description": "Annotations are extra annotations for this resource."}}, "additionalProperties": false, "type": "object"}, "ControlPlaneHeadlessService": {"properties": {"annotations": {"additionalProperties": {"type": "string"}, "type": "object", "description": "Annotations are extra annotations for this resource."}, "labels": {"additionalProperties": {"type": "string"}, "type": "object", "description": "Labels are extra labels for this resource."}}, "additionalProperties": false, "type": "object"}, "ControlPlaneHighAvailability": {"properties": {"replicas": {"type": "integer", "description": "Replicas is the amount of replicas to use for the statefulSet."}, "leaseDuration": {"type": "integer", "description": "LeaseDuration is the time to lease for the leader."}, "renewDeadline": {"type": "integer", "description": "RenewDeadline is the deadline to renew a lease for the leader."}, "retryPeriod": {"type": "integer", "description": "RetryPeriod is the time until a replica will retry to get a lease."}}, "additionalProperties": false, "type": "object"}, "ControlPlaneIngress": {"properties": {"enabled": {"type": "boolean", "description": "Enabled defines if the control plane ingress should be enabled"}, "host": {"type": "string", "description": "Host is the host where vCluster will be reachable"}, "pathType": {"type": "string", "description": "PathType is the path type of the ingress"}, "spec": {"type": "object", "description": "Spec allows you to configure extra ingress options."}, "annotations": {"additionalProperties": {"type": "string"}, "type": "object", "description": "Annotations are extra annotations for this resource."}, "labels": {"additionalProperties": {"type": "string"}, "type": "object", "description": "Labels are extra labels for this resource."}}, "additionalProperties": false, "type": "object"}, "ControlPlanePersistence": {"properties": {"volumeClaim": {"$ref": "#/$defs/VolumeClaim", "description": "VolumeClaim can be used to configure the persistent volume claim."}, "volumeClaimTemplates": {"items": {"type": "object"}, "type": "array", "description": "VolumeClaimTemplates defines the volumeClaimTemplates for the statefulSet"}, "dataVolume": {"items": {"type": "object"}, "type": "array", "description": "Allows you to override the dataVolume. Only works correctly if volumeClaim.enabled=false."}, "binariesVolume": {"items": {"type": "object"}, "type": "array", "description": "BinariesVolume defines a binaries volume that is used to retrieve\ndistro specific executables to be run by the syncer controller.\nThis volume doesn't need to be persistent."}, "addVolumes": {"items": {"type": "object"}, "type": "array", "description": "AddVolumes defines extra volumes for the pod"}, "addVolumeMounts": {"items": {"$ref": "#/$defs/VolumeMount"}, "type": "array", "description": "AddVolumeMounts defines extra volume mounts for the container"}}, "additionalProperties": false, "type": "object"}, "ControlPlaneProbes": {"properties": {"livenessProbe": {"$ref": "#/$defs/LivenessProbe", "description": "LivenessProbe specifies if the liveness probe for the container should be enabled"}, "readinessProbe": {"$ref": "#/$defs/ReadinessProbe", "description": "ReadinessProbe specifies if the readiness probe for the container should be enabled"}, "startupProbe": {"$ref": "#/$defs/StartupProbe", "description": "StartupProbe specifies if the startup probe for the container should be enabled"}}, "additionalProperties": false, "type": "object"}, "ControlPlaneProxy": {"properties": {"bindAddress": {"type": "string", "description": "BindAddress under which vCluster will expose the proxy."}, "port": {"type": "integer", "description": "Port under which vCluster will expose the proxy. Changing port is currently not supported."}, "extraSANs": {"items": {"type": "string"}, "type": "array", "description": "ExtraSANs are extra hostnames to sign the vCluster proxy certificate for."}}, "additionalProperties": false, "type": "object"}, "ControlPlaneScheduling": {"properties": {"nodeSelector": {"type": "object", "description": "NodeSelector is the node selector to apply to the pod."}, "affinity": {"type": "object", "description": "Affinity is the affinity to apply to the pod."}, "tolerations": {"items": {"type": "object"}, "type": "array", "description": "Tolerations are the tolerations to apply to the pod."}, "priorityClassName": {"type": "string", "description": "PriorityClassName is the priority class name for the the pod."}, "podManagementPolicy": {"type": "string", "description": "PodManagementPolicy is the statefulSet pod management policy."}, "topologySpreadConstraints": {"items": true, "type": "array", "description": "TopologySpreadConstraints are the topology spread constraints for the pod."}}, "additionalProperties": false, "type": "object"}, "ControlPlaneSecurity": {"properties": {"podSecurityContext": {"type": "object", "description": "PodSecurityContext specifies security context options on the pod level."}, "containerSecurityContext": {"type": "object", "description": "ContainerSecurityContext specifies security context options on the container level."}}, "additionalProperties": false, "type": "object"}, "ControlPlaneService": {"properties": {"enabled": {"type": "boolean", "description": "Enabled defines if the control plane service should be enabled"}, "spec": {"type": "object", "description": "Spec allows you to configure extra service options."}, "kubeletNodePort": {"type": "integer", "description": "KubeletNodePort is the node port where the fake kubelet is exposed. Defaults to 0."}, "httpsNodePort": {"type": "integer", "description": "HTTPSNodePort is the node port where https is exposed. Defaults to 0."}, "annotations": {"additionalProperties": {"type": "string"}, "type": "object", "description": "Annotations are extra annotations for this resource."}, "labels": {"additionalProperties": {"type": "string"}, "type": "object", "description": "Labels are extra labels for this resource."}}, "additionalProperties": false, "type": "object"}, "ControlPlaneServiceAccount": {"properties": {"enabled": {"type": "boolean", "description": "Enabled specifies if the service account should get deployed."}, "name": {"type": "string", "description": "Name specifies what name to use for the service account."}, "imagePullSecrets": {"items": {"$ref": "#/$defs/ImagePullSecretName"}, "type": "array", "description": "ImagePullSecrets defines extra image pull secrets for the service account."}, "annotations": {"additionalProperties": {"type": "string"}, "type": "object", "description": "Annotations are extra annotations for this resource."}, "labels": {"additionalProperties": {"type": "string"}, "type": "object", "description": "Labels are extra labels for this resource."}}, "additionalProperties": false, "type": "object"}, "ControlPlaneStatefulSet": {"properties": {"highAvailability": {"$ref": "#/$defs/ControlPlaneHighAvailability", "description": "HighAvailability holds options related to high availability."}, "resources": {"$ref": "#/$defs/Resources", "description": "Resources are the resource requests and limits for the statefulSet container."}, "scheduling": {"$ref": "#/$defs/ControlPlaneScheduling", "description": "Scheduling holds options related to scheduling."}, "security": {"$ref": "#/$defs/ControlPlaneSecurity", "description": "Security defines pod or container security context."}, "probes": {"$ref": "#/$defs/ControlPlaneProbes", "description": "Probes enables or disables the main container probes."}, "persistence": {"$ref": "#/$defs/ControlPlanePersistence", "description": "Persistence defines options around persistence for the statefulSet."}, "enableServiceLinks": {"type": "boolean", "description": "EnableServiceLinks for the StatefulSet pod"}, "annotations": {"additionalProperties": {"type": "string"}, "type": "object", "description": "Annotations are extra annotations for this resource."}, "labels": {"additionalProperties": {"type": "string"}, "type": "object", "description": "Labels are extra labels for this resource."}, "pods": {"$ref": "#/$defs/LabelsAndAnnotations", "description": "Additional labels or annotations for the statefulSet pods."}, "image": {"$ref": "#/$defs/Image", "description": "Image is the image for the controlPlane statefulSet container\nIt defaults to the vCluster pro repository that includes the optional pro modules that are turned off by default.\nIf you still want to use the pure OSS build, set the repository to 'loft-sh/vcluster-oss'."}, "imagePullPolicy": {"type": "string", "description": "ImagePullPolicy is the policy how to pull the image."}, "workingDir": {"type": "string", "description": "WorkingDir specifies in what folder the main process should get started."}, "command": {"items": {"type": "string"}, "type": "array", "description": "Command allows you to override the main command."}, "args": {"items": {"type": "string"}, "type": "array", "description": "Arg<PERSON> allows you to override the main arguments."}, "env": {"items": {"type": "object"}, "type": "array", "description": "Env are additional environment variables for the statefulSet container."}, "dnsPolicy": {"type": "string", "description": "Set DNS policy for the pod."}, "dnsConfig": {"$ref": "#/$defs/PodDNSConfig", "description": "Specifies the DNS parameters of a pod."}}, "additionalProperties": false, "type": "object"}, "ControlPlaneWorkloadServiceAccount": {"properties": {"enabled": {"type": "boolean", "description": "Enabled specifies if the service account for the workloads should get deployed."}, "name": {"type": "string", "description": "Name specifies what name to use for the service account for the virtual cluster workloads."}, "imagePullSecrets": {"items": {"$ref": "#/$defs/ImagePullSecretName"}, "type": "array", "description": "ImagePullSecrets defines extra image pull secrets for the workload service account."}, "annotations": {"additionalProperties": {"type": "string"}, "type": "object", "description": "Annotations are extra annotations for this resource."}, "labels": {"additionalProperties": {"type": "string"}, "type": "object", "description": "Labels are extra labels for this resource."}}, "additionalProperties": false, "type": "object"}, "CoreDNS": {"properties": {"enabled": {"type": "boolean", "description": "Enabled defines if coredns is enabled"}, "embedded": {"type": "boolean", "description": "Embedded defines if vCluster will start the embedded coredns service within the control-plane and not as a separate deployment. This is a PRO feature.", "pro": true}, "security": {"$ref": "#/$defs/ControlPlaneSecurity", "description": "Security defines pod or container security context."}, "service": {"$ref": "#/$defs/CoreDNSService", "description": "Service holds extra options for the coredns service deployed within the virtual cluster"}, "deployment": {"$ref": "#/$defs/CoreDNSDeployment", "description": "Deployment holds extra options for the coredns deployment deployed within the virtual cluster"}, "overwriteConfig": {"type": "string", "description": "OverwriteConfig can be used to overwrite the coredns config"}, "overwriteManifests": {"type": "string", "description": "OverwriteManifests can be used to overwrite the coredns manifests used to deploy coredns"}, "priorityClassName": {"type": "string", "description": "PriorityClassName specifies the priority class name for the CoreDNS pods."}}, "additionalProperties": false, "type": "object"}, "CoreDNSDeployment": {"properties": {"image": {"type": "string", "description": "Image is the coredns image to use"}, "replicas": {"type": "integer", "description": "Replicas is the amount of coredns pods to run."}, "nodeSelector": {"additionalProperties": {"type": "string"}, "type": "object", "description": "NodeSelector is the node selector to use for coredns."}, "affinity": {"type": "object", "description": "Affinity is the affinity to apply to the pod."}, "tolerations": {"items": {"type": "object"}, "type": "array", "description": "Tolerations are the tolerations to apply to the pod."}, "resources": {"$ref": "#/$defs/Resources", "description": "Resources are the desired resources for coredns."}, "pods": {"$ref": "#/$defs/LabelsAndAnnotations", "description": "Pods is additional metadata for the coredns pods."}, "annotations": {"additionalProperties": {"type": "string"}, "type": "object", "description": "Annotations are extra annotations for this resource."}, "labels": {"additionalProperties": {"type": "string"}, "type": "object", "description": "Labels are extra labels for this resource."}, "topologySpreadConstraints": {"items": true, "type": "array", "description": "TopologySpreadConstraints are the topology spread constraints for the CoreDNS pod."}}, "additionalProperties": false, "type": "object"}, "CoreDNSService": {"properties": {"spec": {"type": "object", "description": "Spec holds extra options for the coredns service"}, "annotations": {"additionalProperties": {"type": "string"}, "type": "object", "description": "Annotations are extra annotations for this resource."}, "labels": {"additionalProperties": {"type": "string"}, "type": "object", "description": "Labels are extra labels for this resource."}}, "additionalProperties": false, "type": "object"}, "Database": {"properties": {"embedded": {"$ref": "#/$defs/DatabaseKine", "description": "Embedded defines that an embedded database (sqlite) should be used as the backend for the virtual cluster"}, "external": {"$ref": "#/$defs/ExternalDatabaseKine", "description": "External defines that an external database should be used as the backend for the virtual cluster"}}, "additionalProperties": false, "type": "object"}, "DatabaseKine": {"properties": {"enabled": {"type": "boolean", "description": "Enabled defines if the database should be used."}, "dataSource": {"type": "string", "description": "DataSource is the kine dataSource to use for the database. This depends on the database format.\nThis is optional for the embedded database. Examples:\n* mysql: mysql://username:password@tcp(hostname:3306)/k3s\n* postgres: ******************************************/k3s"}, "keyFile": {"type": "string", "description": "KeyFile is the key file to use for the database. This is optional."}, "certFile": {"type": "string", "description": "CertFile is the cert file to use for the database. This is optional."}, "caFile": {"type": "string", "description": "CaFile is the ca file to use for the database. This is optional."}, "extraArgs": {"items": {"type": "string"}, "type": "array", "description": "ExtraArgs are additional arguments to pass to <PERSON><PERSON>."}}, "additionalProperties": false, "type": "object"}, "DenyRule": {"properties": {"name": {"type": "string", "description": "The name of the check."}, "namespaces": {"items": {"type": "string"}, "type": "array", "description": "Namespace describe a list of namespaces that will be affected by the check.\nAn empty list means that all namespaces will be affected.\nIn case of ClusterScoped rules, only the Namespace resource is affected."}, "rules": {"items": {"$ref": "#/$defs/RuleWithVerbs"}, "type": "array", "description": "Rules describes on which verbs and on what resources/subresources the webhook is enforced.\nThe webhook is enforced if it matches any Rule.\nThe version of the request must match the rule version exactly. Equivalent matching is not supported."}, "excludedUsers": {"items": {"type": "string"}, "type": "array", "description": "ExcludedUsers describe a list of users for which the checks will be skipped.\nImpersonation attempts on these users will still be subjected to the checks."}}, "additionalProperties": false, "type": "object"}, "Deploy": {"properties": {"kubeProxy": {"$ref": "#/$defs/KubeProxy", "description": "KubeProxy holds dedicated kube proxy configuration."}, "metallb": {"$ref": "#/$defs/Metallb", "description": "Metallb holds dedicated metallb configuration."}, "cni": {"$ref": "#/$defs/CNI", "description": "CNI holds dedicated CNI configuration."}, "localPathProvisioner": {"$ref": "#/$defs/LocalPathProvisioner", "description": "LocalPathProvisioner holds dedicated local path provisioner configuration."}, "ingressNginx": {"$ref": "#/$defs/IngressNginx", "description": "IngressNginx holds dedicated ingress-nginx configuration."}, "metricsServer": {"$ref": "#/$defs/DeployMetricsServer", "description": "MetricsServer holds dedicated metrics server configuration."}, "volumeSnapshotController": {"$ref": "#/$defs/VolumeSnapshotController", "description": "VolumeSnapshotController holds dedicated CSI snapshot-controller configuration."}}, "additionalProperties": false, "type": "object"}, "DeployMetricsServer": {"properties": {"enabled": {"type": "boolean", "description": "Enabled defines if metrics server should be enabled."}}, "additionalProperties": false, "type": "object"}, "Distro": {"properties": {"k8s": {"$ref": "#/$defs/DistroK8s", "description": "K8S holds K8s relevant configuration."}, "k3s": {"$ref": "#/$defs/DistroK3s", "description": "[Deprecated] K3S holds K3s relevant configuration."}}, "additionalProperties": false, "type": "object"}, "DistroContainerEnabled": {"properties": {"enabled": {"type": "boolean", "description": "Enabled signals this container should be enabled."}, "command": {"items": {"type": "string"}, "type": "array", "description": "Command is the command to start the distro binary. This will override the existing command."}, "extraArgs": {"items": {"type": "string"}, "type": "array", "description": "ExtraArgs are additional arguments to pass to the distro binary."}}, "additionalProperties": false, "type": "object"}, "DistroK3s": {"properties": {"enabled": {"type": "boolean", "description": "Enabled specifies if the K3s distro should be enabled. Only one distro can be enabled at the same time."}, "token": {"type": "string", "description": "Token is the K3s token to use. If empty, vCluster will choose one."}, "image": {"$ref": "#/$defs/Image", "description": "Image is the distro image"}, "imagePullPolicy": {"type": "string", "description": "ImagePullPolicy is the pull policy for the distro image"}, "env": {"items": {"type": "object"}, "type": "array", "description": "Env are extra environment variables to use for the main container and NOT the init container."}, "resources": {"type": "object", "description": "Resources for the distro init container"}, "securityContext": {"type": "object", "description": "Security options can be used for the distro init container"}, "command": {"items": {"type": "string"}, "type": "array", "description": "Command is the command to start the distro binary. This will override the existing command."}, "extraArgs": {"items": {"type": "string"}, "type": "array", "description": "ExtraArgs are additional arguments to pass to the distro binary."}}, "additionalProperties": false, "type": "object"}, "DistroK8s": {"properties": {"enabled": {"type": "boolean", "description": "Enabled specifies if the K8s distro should be enabled. Only one distro can be enabled at the same time."}, "version": {"type": "string", "description": "[Deprecated] Version field is deprecated.\nUse controlPlane.distro.k8s.image.tag to specify the Kubernetes version instead."}, "apiServer": {"$ref": "#/$defs/DistroContainerEnabled", "description": "APIServer holds configuration specific to starting the api server."}, "controllerManager": {"$ref": "#/$defs/DistroContainerEnabled", "description": "ControllerManager holds configuration specific to starting the controller manager."}, "scheduler": {"$ref": "#/$defs/DistroContainerEnabled", "description": "Scheduler holds configuration specific to starting the scheduler."}, "image": {"$ref": "#/$defs/Image", "description": "Image is the distro image"}, "imagePullPolicy": {"type": "string", "description": "ImagePullPolicy is the pull policy for the distro image"}, "env": {"items": {"type": "object"}, "type": "array", "description": "Env are extra environment variables to use for the main container and NOT the init container."}, "resources": {"type": "object", "description": "Resources for the distro init container"}, "securityContext": {"type": "object", "description": "Security options can be used for the distro init container"}}, "additionalProperties": false, "type": "object"}, "DynamicNodePool": {"properties": {"name": {"type": "string", "description": "Name is the name of this NodePool"}, "nodeTypeSelector": {"items": {"$ref": "#/$defs/Requirement"}, "type": "array", "description": "NodeTypeSelector filters the types of nodes that can be provisioned by this pool.\nAll requirements must be met for a node type to be eligible."}, "taints": {"items": {"$ref": "#/$defs/KubeletJoinTaint"}, "type": "array", "description": "Taints are the taints to apply to the nodes in this pool."}, "nodeLabels": {"additionalProperties": {"type": "string"}, "type": "object", "description": "NodeLabels are the labels to apply to the nodes in this pool."}, "limits": {"additionalProperties": {"type": "string"}, "type": "object", "description": "Limits specify the maximum resources that can be provisioned by this node pool,\nmapping to the 'limits' field in Karpenter's NodePool API."}, "disruption": {"$ref": "#/$defs/DynamicNodePoolDisruption", "description": "Disruption contains the parameters that relate to <PERSON><PERSON><PERSON>'s disruption logic"}, "terminationGracePeriod": {"type": "string", "description": "TerminationGracePeriod is the maximum duration the controller will wait before forcefully deleting the pods on a node, measured from when deletion is first initiated.\n\nWarning: this feature takes precedence over a Pod's terminationGracePeriodSeconds value, and bypasses any blocked PDBs or the karpenter.sh/do-not-disrupt annotation.\n\nThis field is intended to be used by cluster administrators to enforce that nodes can be cycled within a given time period.\nWhen set, drifted nodes will begin draining even if there are pods blocking eviction. Draining will respect PDBs and the do-not-disrupt annotation until the TGP is reached.\n\nKarpenter will preemptively delete pods so their terminationGracePeriodSeconds align with the node's terminationGracePeriod.\nIf a pod would be terminated without being granted its full terminationGracePeriodSeconds prior to the node timeout,\nthat pod will be deleted at T = node timeout - pod terminationGracePeriodSeconds.\n\nThe feature can also be used to allow maximum time limits for long-running jobs which can delay node termination with preStop hooks.\nDefaults to 30s. Set to Never to wait indefinitely for pods to be drained."}, "expireAfter": {"type": "string", "description": "The amount of time a Node can live on the cluster before being removed"}, "weight": {"type": "integer", "description": "Weight is the weight of this node pool."}}, "additionalProperties": false, "type": "object", "required": ["name"]}, "DynamicNodePoolDisruption": {"properties": {"consolidateAfter": {"type": "string", "description": "ConsolidateAfter is the duration the controller will wait\nbefore attempting to terminate nodes that are underutilized.\nRefer to ConsolidationPolicy for how underutilization is considered."}, "consolidationPolicy": {"type": "string", "description": "ConsolidationPolicy describes which nodes <PERSON><PERSON><PERSON> can disrupt through its consolidation\nalgorithm. This policy defaults to \"WhenEmptyOrUnderutilized\" if not specified"}, "budgets": {"items": {"$ref": "#/$defs/DynamicNodePoolDisruptionBudget"}, "type": "array", "description": "Budgets is a list of Budgets.\nIf there are multiple active budgets, <PERSON><PERSON><PERSON> uses\nthe most restrictive value. If left undefined,\nthis will default to one budget with a value to 10%."}}, "additionalProperties": false, "type": "object"}, "DynamicNodePoolDisruptionBudget": {"properties": {"nodes": {"type": "string", "description": "Nodes dictates the maximum number of NodeClaims owned by this NodePool\nthat can be terminating at once. This is calculated by counting nodes that\nhave a deletion timestamp set, or are actively being deleted by Karpenter.\nThis field is required when specifying a budget."}, "schedule": {"type": "string", "description": "Schedule specifies when a budget begins being active, following\nthe upstream cronjob syntax. If omitted, the budget is always active.\nTimezones are not supported."}, "duration": {"type": "string", "description": "Duration determines how long a Budget is active since each Schedule hit.\nOnly minutes and hours are accepted, as cron does not work in seconds.\nIf omitted, the budget is always active.\nThis is required if Schedule is set."}}, "additionalProperties": false, "type": "object"}, "EnableAutoSwitch": {"properties": {"enabled": {"oneOf": [{"type": "string"}, {"type": "boolean"}], "description": "Enabled defines if this option should be enabled."}}, "additionalProperties": false, "type": "object"}, "EnableAutoSwitchWithPatches": {"properties": {"enabled": {"oneOf": [{"type": "string"}, {"type": "boolean"}], "description": "Enabled defines if this option should be enabled."}, "patches": {"items": {"$ref": "#/$defs/TranslatePatch"}, "type": "array", "description": "Patches patch the resource according to the provided specification."}}, "additionalProperties": false, "type": "object"}, "EnableAutoSwitchWithPatchesAndSelector": {"properties": {"enabled": {"oneOf": [{"type": "string"}, {"type": "boolean"}], "description": "Enabled defines if this option should be enabled."}, "patches": {"items": {"$ref": "#/$defs/TranslatePatch"}, "type": "array", "description": "Patches patch the resource according to the provided specification."}, "selector": {"$ref": "#/$defs/StandardLabelSelector", "description": "Selector defines the selector to use for the resource. If not set, all resources of that type will be synced."}}, "additionalProperties": false, "type": "object"}, "EnableSwitch": {"properties": {"enabled": {"type": "boolean", "description": "Enabled defines if this option should be enabled."}}, "additionalProperties": false, "type": "object"}, "EnableSwitchSelector": {"properties": {"selector": {"$ref": "#/$defs/StandardLabelSelector"}, "enabled": {"type": "boolean", "description": "Enabled defines if this option should be enabled."}}, "additionalProperties": false, "type": "object"}, "EnableSwitchWithPatches": {"properties": {"enabled": {"type": "boolean", "description": "Enabled defines if this option should be enabled."}, "patches": {"items": {"$ref": "#/$defs/TranslatePatch"}, "type": "array", "description": "Patches patch the resource according to the provided specification."}}, "additionalProperties": false, "type": "object"}, "EnableSwitchWithPatchesAndSelector": {"properties": {"enabled": {"type": "boolean", "description": "Enabled defines if this option should be enabled."}, "patches": {"items": {"$ref": "#/$defs/TranslatePatch"}, "type": "array", "description": "Patches patch the resource according to the provided specification."}, "selector": {"$ref": "#/$defs/StandardLabelSelector", "description": "Selector defines the selector to use for the resource. If not set, all resources of that type will be synced."}}, "additionalProperties": false, "type": "object"}, "EnableSwitchWithResourcesMappings": {"properties": {"enabled": {"type": "boolean", "description": "Enabled defines if this option should be enabled."}, "patches": {"items": {"$ref": "#/$defs/TranslatePatch"}, "type": "array", "description": "Patches patch the resource according to the provided specification."}, "mappings": {"$ref": "#/$defs/FromHostMappings", "description": "Mappings for Namespace and Object"}}, "additionalProperties": false, "type": "object"}, "Etcd": {"properties": {"embedded": {"$ref": "#/$defs/EtcdEmbedded", "description": "Embedded defines to use embedded etcd as a storage backend for the virtual cluster", "pro": true}, "deploy": {"$ref": "#/$defs/EtcdDeploy", "description": "Deploy defines to use an external etcd that is deployed by the helm chart"}, "external": {"$ref": "#/$defs/EtcdExternal", "description": "External defines to use a self-hosted external etcd that is not deployed by the helm chart"}}, "additionalProperties": false, "type": "object"}, "EtcdDeploy": {"properties": {"enabled": {"type": "boolean", "description": "Enabled defines that an external etcd should be deployed."}, "statefulSet": {"$ref": "#/$defs/EtcdDeployStatefulSet", "description": "StatefulSet holds options for the external etcd statefulSet."}, "service": {"$ref": "#/$defs/EtcdDeployService", "description": "Service holds options for the external etcd service."}, "headlessService": {"$ref": "#/$defs/EtcdDeployHeadlessService", "description": "HeadlessService holds options for the external etcd headless service."}}, "additionalProperties": false, "type": "object"}, "EtcdDeployHeadlessService": {"properties": {"annotations": {"additionalProperties": {"type": "string"}, "type": "object", "description": "Annotations are extra annotations for the external etcd headless service"}}, "additionalProperties": false, "type": "object"}, "EtcdDeployService": {"properties": {"enabled": {"type": "boolean", "description": "Enabled defines if the etcd service should be deployed"}, "annotations": {"additionalProperties": {"type": "string"}, "type": "object", "description": "Annotations are extra annotations for the external etcd service"}}, "additionalProperties": false, "type": "object"}, "EtcdDeployStatefulSet": {"properties": {"enabled": {"type": "boolean", "description": "Enabled defines if the statefulSet should be deployed"}, "enableServiceLinks": {"type": "boolean", "description": "EnableServiceLinks for the StatefulSet pod"}, "image": {"$ref": "#/$defs/Image", "description": "Image is the image to use for the external etcd statefulSet"}, "imagePullPolicy": {"type": "string", "description": "ImagePullPolicy is the pull policy for the external etcd image"}, "env": {"items": {"type": "object"}, "type": "array", "description": "Env are extra environment variables"}, "extraArgs": {"items": {"type": "string"}, "type": "array", "description": "ExtraArgs are appended to the etcd command."}, "resources": {"$ref": "#/$defs/Resources", "description": "Resources the etcd can consume"}, "pods": {"$ref": "#/$defs/LabelsAndAnnotations", "description": "Pods defines extra metadata for the etcd pods."}, "highAvailability": {"$ref": "#/$defs/ExternalEtcdHighAvailability", "description": "HighAvailability are high availability options"}, "scheduling": {"$ref": "#/$defs/ControlPlaneScheduling", "description": "Scheduling options for the etcd pods."}, "security": {"$ref": "#/$defs/ControlPlaneSecurity", "description": "Security options for the etcd pods."}, "persistence": {"$ref": "#/$defs/ExternalEtcdPersistence", "description": "Persistence options for the etcd pods."}, "annotations": {"additionalProperties": {"type": "string"}, "type": "object", "description": "Annotations are extra annotations for this resource."}, "labels": {"additionalProperties": {"type": "string"}, "type": "object", "description": "Labels are extra labels for this resource."}}, "additionalProperties": false, "type": "object"}, "EtcdEmbedded": {"properties": {"enabled": {"type": "boolean", "description": "Enabled defines if the embedded etcd should be used.", "pro": true}, "migrateFromDeployedEtcd": {"type": "boolean", "description": "MigrateFromDeployedEtcd signals that vCluster should migrate from the deployed external etcd to embedded etcd."}, "snapshotCount": {"type": "integer", "description": "SnapshotCount defines the number of snapshots to keep for the embedded etcd. Defaults to 10000 if less than 1."}, "extraArgs": {"items": {"type": "string"}, "type": "array", "description": "ExtraArgs are additional arguments to pass to the embedded etcd."}}, "additionalProperties": false, "type": "object"}, "EtcdExternal": {"properties": {"enabled": {"type": "boolean", "description": "Enabled defines if the external etcd should be used."}, "endpoint": {"type": "string", "description": "Endpoint holds the endpoint of the external etcd server, e.g. my-example-service:2379"}, "tls": {"$ref": "#/$defs/EtcdExternalTLS", "description": "TLS defines the tls configuration for the external etcd server"}}, "additionalProperties": false, "type": "object"}, "EtcdExternalTLS": {"properties": {"caFile": {"type": "string", "description": "CaFile is the path to the ca file"}, "certFile": {"type": "string", "description": "CertFile is the path to the cert file"}, "keyFile": {"type": "string", "description": "KeyFile is the path to the key file"}}, "additionalProperties": false, "type": "object", "description": "EtcdExternalTLS defines tls for external etcd server"}, "Experimental": {"properties": {"deploy": {"$ref": "#/$defs/ExperimentalDeploy", "description": "Deploy allows you to configure manifests and Helm charts to deploy within the host or virtual cluster."}, "syncSettings": {"$ref": "#/$defs/ExperimentalSyncSettings", "description": "SyncSettings are advanced settings for the syncer controller."}, "virtualClusterKubeConfig": {"$ref": "#/$defs/VirtualClusterKubeConfig", "description": "VirtualClusterKubeConfig allows you to override distro specifics and specify where vCluster will find the required certificates and vCluster config.\nDeprecated: Removed in 0.29.0."}, "denyProxyRequests": {"items": {"$ref": "#/$defs/DenyRule"}, "type": "array", "description": "DenyProxyRequests denies certain requests in the vCluster proxy.", "pro": true}}, "additionalProperties": false, "type": "object"}, "ExperimentalDeploy": {"properties": {"host": {"$ref": "#/$defs/ExperimentalDeployHost", "description": "Host defines what manifests to deploy into the host cluster"}, "vcluster": {"$ref": "#/$defs/ExperimentalDeployVCluster", "description": "VCluster defines what manifests and charts to deploy into the vCluster"}}, "additionalProperties": false, "type": "object"}, "ExperimentalDeployHelm": {"properties": {"chart": {"$ref": "#/$defs/ExperimentalDeployHelmChart", "description": "Chart defines what chart should get deployed."}, "release": {"$ref": "#/$defs/ExperimentalDeployHelmRelease", "description": "Release defines what release should get deployed."}, "values": {"type": "string", "description": "Values defines what values should get used."}, "timeout": {"type": "string", "description": "Timeout defines the timeout for <PERSON><PERSON>"}, "bundle": {"type": "string", "description": "<PERSON><PERSON><PERSON> allows to compress the Helm chart and specify this instead of an online chart"}}, "additionalProperties": false, "type": "object"}, "ExperimentalDeployHelmChart": {"properties": {"name": {"type": "string"}, "repo": {"type": "string"}, "insecure": {"type": "boolean"}, "version": {"type": "string"}, "username": {"type": "string"}, "password": {"type": "string"}}, "additionalProperties": false, "type": "object"}, "ExperimentalDeployHelmRelease": {"properties": {"name": {"type": "string", "description": "Name of the release"}, "namespace": {"type": "string", "description": "Namespace of the release"}}, "additionalProperties": false, "type": "object"}, "ExperimentalDeployHost": {"properties": {"manifests": {"type": "string", "description": "Manifests are raw Kubernetes manifests that should get applied within the host cluster."}, "manifestsTemplate": {"type": "string", "description": "ManifestsTemplate is a Kubernetes manifest template that will be rendered with vCluster values before applying it within the host cluster."}}, "additionalProperties": false, "type": "object"}, "ExperimentalDeployVCluster": {"properties": {"manifests": {"type": "string", "description": "Manifests are raw Kubernetes manifests that should get applied within the virtual cluster."}, "manifestsTemplate": {"type": "string", "description": "ManifestsTemplate is a Kubernetes manifest template that will be rendered with vCluster values before applying it within the virtual cluster."}, "helm": {"items": {"$ref": "#/$defs/ExperimentalDeployHelm"}, "type": "array", "description": "Helm are Helm charts that should get deployed into the virtual cluster"}}, "additionalProperties": false, "type": "object"}, "ExperimentalSyncSettings": {"properties": {"setOwner": {"type": "boolean", "description": "SetOwner specifies if vCluster should set an owner reference on the synced objects to the vCluster service. This allows for easy garbage collection."}, "hostMetricsBindAddress": {"type": "string", "description": "HostMetricsBindAddress is the bind address for the local manager"}, "virtualMetricsBindAddress": {"type": "string", "description": "VirtualMetricsBindAddress is the bind address for the virtual manager"}}, "additionalProperties": false, "type": "object"}, "ExportKubeConfig": {"properties": {"context": {"type": "string", "description": "Context is the name of the context within the generated kubeconfig to use."}, "server": {"type": "string", "description": "Override the default https://localhost:8443 and specify a custom hostname for the generated kubeconfig."}, "insecure": {"type": "boolean", "description": "If tls should get skipped for the server"}, "serviceAccount": {"$ref": "#/$defs/ExportKubeConfigServiceAccount", "description": "ServiceAccount can be used to generate a service account token instead of the default certificates."}, "secret": {"$ref": "#/$defs/ExportKubeConfigSecretReference", "description": "Declare in which host cluster secret vCluster should store the generated virtual cluster kubeconfig.\nIf this is not defined, vCluster will create it with `vc-NAME`. If you specify another name,\nvCluster creates the config in this other secret.\n\nDeprecated: Use AdditionalSecrets instead."}, "additionalSecrets": {"items": {"$ref": "#/$defs/ExportKubeConfigAdditionalSecretReference"}, "type": "array", "description": "AdditionalSecrets specifies the additional host cluster secrets in which vCluster will store the\ngenerated virtual cluster kubeconfigs."}}, "additionalProperties": false, "type": "object", "description": "ExportKubeConfig describes how vCluster should export the vCluster kubeconfig."}, "ExportKubeConfigAdditionalSecretReference": {"properties": {"context": {"type": "string", "description": "Context is the name of the context within the generated kubeconfig to use."}, "server": {"type": "string", "description": "Override the default https://localhost:8443 and specify a custom hostname for the generated kubeconfig."}, "insecure": {"type": "boolean", "description": "If tls should get skipped for the server"}, "serviceAccount": {"$ref": "#/$defs/ExportKubeConfigServiceAccount", "description": "ServiceAccount can be used to generate a service account token instead of the default certificates."}, "name": {"type": "string", "description": "Name is the name of the secret where the kubeconfig is stored."}, "namespace": {"type": "string", "description": "Namespace where vCluster stores the kubeconfig secret. If this is not equal to the namespace\nwhere you deployed vCluster, you need to make sure vCluster has access to this other namespace."}}, "additionalProperties": false, "type": "object", "description": "ExportKubeConfigAdditionalSecretReference defines the additional host cluster secret in which vCluster stores the generated virtual cluster kubeconfigs."}, "ExportKubeConfigSecretReference": {"properties": {"name": {"type": "string", "description": "Name is the name of the secret where the kubeconfig should get stored."}, "namespace": {"type": "string", "description": "Namespace where vCluster should store the kubeconfig secret. If this is not equal to the namespace\nwhere you deployed vCluster, you need to make sure vCluster has access to this other namespace."}}, "additionalProperties": false, "type": "object", "description": "Declare in which host cluster secret vCluster should store the generated virtual cluster kubeconfig."}, "ExportKubeConfigServiceAccount": {"properties": {"name": {"type": "string", "description": "Name of the service account to be used to generate a service account token instead of the default certificates."}, "namespace": {"type": "string", "description": "Namespace of the service account to be used to generate a service account token instead of the default certificates.\nIf omitted, will use the kube-system namespace."}, "clusterRole": {"type": "string", "description": "ClusterRole to assign to the service account."}}, "additionalProperties": false, "type": "object"}, "ExternalConfig": {"properties": {"platform": {"$ref": "#/$defs/PlatformConfig", "type": "object", "description": "platform holds platform configuration"}}, "type": "object", "description": "ExternalConfig holds external configuration"}, "ExternalDatabaseKine": {"properties": {"enabled": {"type": "boolean", "description": "Enabled defines if the database should be used."}, "dataSource": {"type": "string", "description": "DataSource is the kine dataSource to use for the database. This depends on the database format.\nThis is optional for the embedded database. Examples:\n* mysql: mysql://username:password@tcp(hostname:3306)/k3s\n* postgres: ******************************************/k3s"}, "keyFile": {"type": "string", "description": "KeyFile is the key file to use for the database. This is optional."}, "certFile": {"type": "string", "description": "CertFile is the cert file to use for the database. This is optional."}, "caFile": {"type": "string", "description": "CaFile is the ca file to use for the database. This is optional."}, "extraArgs": {"items": {"type": "string"}, "type": "array", "description": "ExtraArgs are additional arguments to pass to <PERSON><PERSON>."}, "connector": {"type": "string", "description": "Connector specifies a secret located in a connected vCluster Platform that contains database server connection information\nto be used by Platform to create a database and database user for the vCluster.\nand non-privileged user. A kine endpoint should be created using the database and user on Platform registration.\nThis is optional."}}, "additionalProperties": false, "type": "object"}, "ExternalEtcdHighAvailability": {"properties": {"replicas": {"type": "integer", "description": "Replicas are the amount of pods to use."}}, "additionalProperties": false, "type": "object"}, "ExternalEtcdPersistence": {"properties": {"volumeClaim": {"$ref": "#/$defs/ExternalEtcdPersistenceVolumeClaim", "description": "VolumeClaim can be used to configure the persistent volume claim."}, "volumeClaimTemplates": {"items": {"type": "object"}, "type": "array", "description": "VolumeClaimTemplates defines the volumeClaimTemplates for the statefulSet"}, "addVolumes": {"items": {"type": "object"}, "type": "array", "description": "AddVolumes defines extra volumes for the pod"}, "addVolumeMounts": {"items": {"$ref": "#/$defs/VolumeMount"}, "type": "array", "description": "AddVolumeMounts defines extra volume mounts for the container"}}, "additionalProperties": false, "type": "object"}, "ExternalEtcdPersistenceVolumeClaim": {"properties": {"enabled": {"type": "boolean", "description": "Enabled enables deploying a persistent volume claim."}, "accessModes": {"items": {"type": "string"}, "type": "array", "description": "AccessModes are the persistent volume claim access modes."}, "retentionPolicy": {"type": "string", "description": "RetentionPolicy is the persistent volume claim retention policy."}, "size": {"type": "string", "description": "Size is the persistent volume claim storage size."}, "storageClass": {"type": "string", "description": "StorageClass is the persistent volume claim storage class."}}, "additionalProperties": false, "type": "object"}, "ExternalSecrets": {"properties": {"enabled": {"type": "boolean", "description": "Enabled defines whether the external secret integration is enabled or not"}, "version": {"type": "string", "description": "Version defines the version of the external secrets operator to use. If empty, the storage version will be used."}, "webhook": {"$ref": "#/$defs/EnableSwitch", "description": "Webhook defines whether the host webhooks are reused or not"}, "sync": {"$ref": "#/$defs/ExternalSecretsSync", "description": "Sync defines the syncing behavior for the integration"}}, "additionalProperties": false, "type": "object", "description": "ExternalSecrets reuses a host external secret operator and makes certain CRDs from it available inside the vCluster"}, "ExternalSecretsSync": {"properties": {"toHost": {"$ref": "#/$defs/ExternalSecretsSyncToHostConfig", "description": "ToHost defines what resources are synced from the virtual cluster to the host"}, "fromHost": {"$ref": "#/$defs/ExternalSecretsSyncFromHostConfig", "description": "FromHost defines what resources are synced from the host cluster to the virtual cluster"}}, "additionalProperties": false, "type": "object"}, "ExternalSecretsSyncFromHostConfig": {"properties": {"clusterStores": {"$ref": "#/$defs/EnableSwitchSelector", "description": "ClusterStores defines if cluster secrets stores should get synced from the host cluster to the virtual cluster."}}, "additionalProperties": false, "type": "object"}, "ExternalSecretsSyncToHostConfig": {"properties": {"externalSecrets": {"$ref": "#/$defs/SelectorConfig", "description": "ExternalSecrets allows to configure if only a subset of ExternalSecrets matching a label selector should get synced from the virtual cluster to the host cluster."}, "stores": {"$ref": "#/$defs/EnableSwitchSelector", "description": "Stores defines if secret stores should get synced from the virtual cluster to the host cluster and then bi-directionally."}}, "additionalProperties": false, "type": "object"}, "FromHostMappings": {"properties": {"byName": {"additionalProperties": {"type": "string"}, "type": "object", "description": "ByName is a map of host-object-namespace/host-object-name: virtual-object-namespace/virtual-object-name.\nThere are several wildcards supported:\n1. To match all objects in host namespace and sync them to different namespace in vCluster:\nbyName:\n  \"foo/*\": \"foo-in-virtual/*\"\n2. To match specific object in the host namespace and sync it to the same namespace with the same name:\nbyName:\n  \"foo/my-object\": \"foo/my-object\"\n3. To match specific object in the host namespace and sync it to the same namespace with different name:\nbyName:\n  \"foo/my-object\": \"foo/my-virtual-object\"\n4. To match all objects in the vCluster host namespace and sync them to a different namespace in vCluster:\nbyName:\n  \"\": \"my-virtual-namespace/*\"\n5. To match specific objects in the vCluster host namespace and sync them to a different namespace in vCluster:\nbyName:\n  \"/my-object\": \"my-virtual-namespace/my-object\""}}, "additionalProperties": false, "type": "object"}, "HostPathMapper": {"properties": {"enabled": {"type": "boolean", "description": "Enabled specifies if the host path mapper will be used"}, "central": {"type": "boolean", "description": "Central specifies if the central host path mapper will be used"}}, "additionalProperties": false, "type": "object"}, "HybridScheduling": {"properties": {"enabled": {"type": "boolean", "description": "Enabled specifies if hybrid scheduling is enabled."}, "hostSchedulers": {"items": {"type": "string"}, "type": "array", "description": "HostSchedulers is a list of schedulers that are deployed on the host cluster."}}, "additionalProperties": false, "type": "object"}, "IPBlock": {"properties": {"cidr": {"type": "string", "description": "cidr is a string representing the IPBlock\nValid examples are \"***********/24\" or \"2001:db8::/64\""}, "except": {"items": {"type": "string"}, "type": "array", "description": "except is a slice of CIDRs that should not be included within an IPBlock\nValid examples are \"***********/24\" or \"2001:db8::/64\"\nExcept values will be rejected if they are outside the cidr range\n+optional"}}, "additionalProperties": false, "type": "object"}, "Image": {"properties": {"registry": {"type": "string", "description": "Registry is the registry of the container image, e.g. my-registry.com or ghcr.io. This setting can be globally\noverridden via the controlPlane.advanced.defaultImageRegistry option. Empty means docker hub."}, "repository": {"type": "string", "description": "Repository is the repository of the container image, e.g. my-repo/my-image"}, "tag": {"type": "string", "description": "Tag is the tag of the container image, and is the default version."}}, "additionalProperties": false, "type": "object"}, "ImagePullSecretName": {"properties": {"name": {"type": "string", "description": "Name of the image pull secret to use."}}, "additionalProperties": false, "type": "object"}, "IngressNginx": {"properties": {"enabled": {"type": "boolean", "description": "Enabled defines if ingress-nginx should be enabled."}, "defaultIngressClass": {"type": "boolean", "description": "DefaultIngressClass defines if the deployed ingress class should be the default ingress class."}}, "additionalProperties": false, "type": "object"}, "Integrations": {"properties": {"metricsServer": {"$ref": "#/$defs/MetricsServer", "description": "MetricsServer reuses the metrics server from the host cluster within the vCluster."}, "kubeVirt": {"$ref": "#/$defs/KubeVirt", "description": "KubeVirt reuses a host kubevirt and makes certain CRDs from it available inside the vCluster"}, "externalSecrets": {"$ref": "#/$defs/ExternalSecrets", "description": "ExternalSecrets reuses a host external secret operator and makes certain CRDs from it available inside the vCluster.\n- ExternalSecrets will be synced from the virtual cluster to the host cluster.\n- SecretStores will be synced from the virtual cluster to the host cluster and then bi-directionally.\n- ClusterSecretStores will be synced from the host cluster to the virtual cluster."}, "certManager": {"$ref": "#/$defs/CertManager", "description": "CertManager reuses a host cert-manager and makes its CRDs from it available inside the vCluster.\n- Certificates and Issuers will be synced from the virtual cluster to the host cluster.\n- ClusterIssuers will be synced from the host cluster to the virtual cluster."}, "istio": {"$ref": "#/$defs/Istio", "description": "Istio syncs DestinationRules, Gateways and VirtualServices from virtual cluster to the host."}, "netris": {"type": "object", "description": "Netris integration helps configuring netris networking for vCluster."}}, "additionalProperties": false, "type": "object", "description": "Integrations holds config for vCluster integrations with other operators or tools running on the host cluster"}, "Istio": {"properties": {"enabled": {"type": "boolean", "description": "Enabled defines if this option should be enabled."}, "sync": {"$ref": "#/$defs/IstioSync"}}, "additionalProperties": false, "type": "object"}, "IstioSync": {"properties": {"toHost": {"$ref": "#/$defs/IstioSyncToHost"}}, "additionalProperties": false, "type": "object"}, "IstioSyncToHost": {"properties": {"destinationRules": {"$ref": "#/$defs/EnableSwitch"}, "gateways": {"$ref": "#/$defs/EnableSwitch"}, "virtualServices": {"$ref": "#/$defs/EnableSwitch"}}, "additionalProperties": false, "type": "object"}, "JoinConfiguration": {"properties": {"preInstallCommands": {"items": {"type": "string"}, "type": "array", "description": "PreInstallCommands are commands that will be executed before containerd, kubelet etc. is installed."}, "preJoinCommands": {"items": {"type": "string"}, "type": "array", "description": "PreJoinCommands are commands that will be executed before kubeadm join is executed."}, "postJoinCommands": {"items": {"type": "string"}, "type": "array", "description": "PostJoinCommands are commands that will be executed after kubeadm join is executed."}, "containerd": {"$ref": "#/$defs/ContainerdJoin", "description": "Containerd holds configuration for the containerd join process."}, "caCertPath": {"type": "string", "description": "CACertPath is the path to the SSL certificate authority used to\nsecure communications between node and control-plane.\nDefaults to \"/etc/kubernetes/pki/ca.crt\"."}, "skipPhases": {"items": {"type": "string"}, "type": "array", "description": "SkipPhases is a list of phases to skip during command execution.\nThe list of phases can be obtained with the \"kubeadm join --help\" command."}, "nodeRegistration": {"$ref": "#/$defs/NodeRegistration", "description": "NodeRegistration holds configuration for the node registration similar to the kubeadm node registration."}}, "additionalProperties": false, "type": "object"}, "Konnectivity": {"properties": {"server": {"$ref": "#/$defs/KonnectivityServer", "description": "Server holds configuration for the konnectivity server."}, "agent": {"$ref": "#/$defs/KonnectivityAgent", "description": "Agent holds configuration for the konnectivity agent."}}, "additionalProperties": false, "type": "object"}, "KonnectivityAgent": {"properties": {"enabled": {"type": "boolean", "description": "Enabled defines if the konnectivity agent should be enabled."}, "replicas": {"type": "integer", "description": "Replicas is the number of replicas for the konnectivity agent."}, "image": {"type": "string", "description": "Image is the image for the konnectivity agent."}, "imagePullPolicy": {"type": "string", "description": "ImagePullPolicy is the policy how to pull the image."}, "nodeSelector": {"additionalProperties": {"type": "string"}, "type": "object", "description": "NodeSelector is the node selector for the konnectivity agent."}, "priorityClassName": {"type": "string", "description": "PriorityClassName is the priority class name for the konnectivity agent."}, "tolerations": {"items": true, "type": "array", "description": "Tolerations is the tolerations for the konnectivity agent."}, "extraEnv": {"items": true, "type": "array", "description": "ExtraEnv is the extra environment variables for the konnectivity agent."}, "extraArgs": {"items": {"type": "string"}, "type": "array", "description": "ExtraArgs are additional arguments to pass to the konnectivity agent."}}, "additionalProperties": false, "type": "object"}, "KonnectivityServer": {"properties": {"enabled": {"type": "boolean", "description": "Enabled defines if the konnectivity server should be enabled."}, "extraArgs": {"items": {"type": "string"}, "type": "array", "description": "ExtraArgs are additional arguments to pass to the konnectivity server."}}, "additionalProperties": false, "type": "object"}, "KubeProxy": {"properties": {"enabled": {"type": "boolean", "description": "Enabled defines if the kube proxy should be enabled."}, "image": {"type": "string", "description": "Image is the image for the kube-proxy."}, "imagePullPolicy": {"type": "string", "description": "ImagePullPolicy is the policy how to pull the image."}, "nodeSelector": {"additionalProperties": {"type": "string"}, "type": "object", "description": "NodeSelector is the node selector for the kube-proxy."}, "priorityClassName": {"type": "string", "description": "PriorityClassName is the priority class name for the kube-proxy."}, "tolerations": {"items": true, "type": "array", "description": "Tolerations is the tolerations for the kube-proxy."}, "extraEnv": {"items": true, "type": "array", "description": "ExtraEnv is the extra environment variables for the kube-proxy."}, "extraArgs": {"items": {"type": "string"}, "type": "array", "description": "ExtraArgs are additional arguments to pass to the kube-proxy."}, "config": {"type": "object", "description": "Config is the config for the kube-proxy that will be merged into the default kube-proxy config. More information can be found here:\nhttps://kubernetes.io/docs/reference/config-api/kube-proxy-config.v1alpha1/#kubeproxy-config-k8s-io-v1alpha1-KubeProxyConfiguration"}}, "additionalProperties": false, "type": "object"}, "KubeVirt": {"properties": {"enabled": {"type": "boolean", "description": "Enabled signals if the integration should be enabled"}, "apiService": {"$ref": "#/$defs/APIService", "description": "APIService holds information about where to find the virt-api service. Defaults to virt-api/kubevirt."}, "webhook": {"$ref": "#/$defs/EnableSwitch", "description": "Webhook holds configuration for enabling the webhook within the vCluster"}, "sync": {"$ref": "#/$defs/KubeVirtSync", "description": "Sync holds configuration on what resources to sync"}}, "additionalProperties": false, "type": "object", "description": "KubeVirt reuses a host kubevirt and makes certain CRDs from it available inside the vCluster"}, "KubeVirtSync": {"properties": {"dataVolumes": {"$ref": "#/$defs/EnableSwitch", "description": "If DataVolumes should get synced"}, "virtualMachineInstanceMigrations": {"$ref": "#/$defs/EnableSwitch", "description": "If VirtualMachineInstanceMigrations should get synced"}, "virtualMachineInstances": {"$ref": "#/$defs/EnableSwitch", "description": "If VirtualMachineInstances should get synced"}, "virtualMachines": {"$ref": "#/$defs/EnableSwitch", "description": "If VirtualMachines should get synced"}, "virtualMachineClones": {"$ref": "#/$defs/EnableSwitch", "description": "If VirtualMachineClones should get synced"}, "virtualMachinePools": {"$ref": "#/$defs/EnableSwitch", "description": "If VirtualMachinePools should get synced"}}, "additionalProperties": false, "type": "object", "description": "KubeVirtSync are the crds that are supported by this integration"}, "Kubelet": {"properties": {"config": {"type": "object", "description": "Config is the config for the kubelet that will be merged into the default kubelet config. More information can be found here:\nhttps://kubernetes.io/docs/reference/config-api/kubelet-config.v1beta1/#kubelet-config-k8s-io-v1beta1-KubeletConfiguration"}}, "additionalProperties": false, "type": "object"}, "KubeletExtraArg": {"properties": {"name": {"type": "string", "description": "Name is the name of the argument."}, "value": {"type": "string", "description": "Value is the value of the argument."}}, "additionalProperties": false, "type": "object", "description": "KubeletExtraArg represents an argument with a name and a value."}, "KubeletJoinTaint": {"properties": {"key": {"type": "string", "description": "Required. The taint key to be applied to a node."}, "value": {"type": "string", "description": "The taint value corresponding to the taint key.\n+optional"}, "effect": {"type": "string", "description": "Required. The effect of the taint on pods\nthat do not tolerate the taint.\nValid effects are NoSchedule, PreferNoSchedule and NoExecute."}}, "additionalProperties": false, "type": "object"}, "LabelSelector": {"properties": {"labels": {"additionalProperties": {"type": "string"}, "type": "object", "description": "Labels defines what labels should be looked for"}}, "additionalProperties": false, "type": "object"}, "LabelSelectorRequirement": {"properties": {"key": {"type": "string"}, "operator": {"type": "string"}, "values": {"items": {"type": "string"}, "type": "array"}}, "additionalProperties": false, "type": "object"}, "LabelsAndAnnotations": {"properties": {"annotations": {"additionalProperties": {"type": "string"}, "type": "object", "description": "Annotations are extra annotations for this resource."}, "labels": {"additionalProperties": {"type": "string"}, "type": "object", "description": "Labels are extra labels for this resource."}}, "additionalProperties": false, "type": "object"}, "LimitRange": {"properties": {"enabled": {"oneOf": [{"type": "string"}, {"type": "boolean"}], "description": "Enabled defines if the limit range should be deployed by vCluster. \"auto\" means that if resourceQuota is enabled,\nthe limitRange will be enabled as well."}, "default": {"type": "object", "description": "Default are the default limits for the limit range"}, "defaultRequest": {"type": "object", "description": "DefaultRequest are the default request options for the limit range"}, "max": {"type": "object", "description": "Max are the max limits for the limit range"}, "min": {"type": "object", "description": "Min are the min limits for the limit range"}, "annotations": {"additionalProperties": {"type": "string"}, "type": "object", "description": "Annotations are extra annotations for this resource."}, "labels": {"additionalProperties": {"type": "string"}, "type": "object", "description": "Labels are extra labels for this resource."}}, "additionalProperties": false, "type": "object"}, "LivenessProbe": {"properties": {"enabled": {"type": "boolean", "description": "Enabled defines if this option should be enabled."}, "failureThreshold": {"type": "integer", "description": "Number of consecutive failures for the probe to be considered failed"}, "initialDelaySeconds": {"type": "integer", "description": "Time (in seconds) to wait before starting the liveness probe"}, "timeoutSeconds": {"type": "integer", "description": "Maximum duration (in seconds) that the probe will wait for a response."}, "periodSeconds": {"type": "integer", "description": "Frequency (in seconds) to perform the probe"}}, "additionalProperties": false, "type": "object", "description": "LivenessProbe defines the configuration for the liveness probe."}, "LocalPathProvisioner": {"properties": {"enabled": {"type": "boolean", "description": "Enabled defines if LocalPathProvisioner should be enabled."}, "image": {"type": "string", "description": "Image is the image for local path provisioner."}, "imagePullPolicy": {"type": "string", "description": "ImagePullPolicy is the policy how to pull the image."}, "nodePath": {"type": "string", "description": "NodePath is the path on the node where to create the persistent volume directories."}}, "additionalProperties": false, "type": "object"}, "Logging": {"properties": {"encoding": {"type": "string", "description": "Encoding specifies the format of vCluster logs, it can either be json or console."}}, "additionalProperties": false, "type": "object", "description": "Logging holds the log encoding details"}, "Metallb": {"properties": {"enabled": {"type": "boolean", "description": "Enabled defines if metallb should be enabled."}, "controllerImage": {"type": "string", "description": "ControllerImage is the image for metallb controller."}, "speakerImage": {"type": "string", "description": "SpeakerImage is the image for metallb speaker."}, "ipAddressPool": {"$ref": "#/$defs/MetallbIPAddressPool", "description": "IPAddressPool is the IP address pool to use for metallb."}}, "additionalProperties": false, "type": "object"}, "MetallbIPAddressPool": {"properties": {"addresses": {"items": {"type": "string"}, "type": "array", "description": "Addresses is a list of IP addresses to use for the IP address pool."}, "l2Advertisement": {"type": "boolean", "description": "L2Advertisement defines if L2 advertisement should be enabled for the IP address pool."}}, "additionalProperties": false, "type": "object"}, "MetricsServer": {"properties": {"enabled": {"type": "boolean", "description": "Enabled signals the metrics server integration should be enabled."}, "apiService": {"$ref": "#/$defs/APIService", "description": "APIService holds information about where to find the metrics-server service. Defaults to metrics-server/kube-system."}, "nodes": {"type": "boolean", "description": "Nodes defines if metrics-server nodes api should get proxied from host to virtual cluster."}, "pods": {"type": "boolean", "description": "Pods defines if metrics-server pods api should get proxied from host to virtual cluster."}}, "additionalProperties": false, "type": "object", "description": "MetricsServer reuses the metrics server from the host cluster within the vCluster."}, "MutatingWebhook": {"properties": {"reinvocationPolicy": {"type": "string", "description": "reinvocationP<PERSON>y indicates whether this webhook should be called multiple times as part of a single admission evaluation.\nAllowed values are \"Never\" and \"IfNeeded\"."}, "name": {"type": "string", "description": "The name of the admission webhook.\nName should be fully qualified, e.g., imagepolicy.kubernetes.io, where\n\"imagepolicy\" is the name of the webhook, and kubernetes.io is the name\nof the organization."}, "clientConfig": {"$ref": "#/$defs/ValidatingWebhookClientConfig", "description": "ClientConfig defines how to communicate with the hook."}, "rules": {"items": true, "type": "array", "description": "Rules describes what operations on what resources/subresources the webhook cares about.\nThe webhook cares about an operation if it matches _any_ Rule."}, "failurePolicy": {"type": "string", "description": "FailurePolicy defines how unrecognized errors from the admission endpoint are handled -\nallowed values are Ignore or Fail. Defaults to Fail."}, "matchPolicy": {"type": "string", "description": "matchPolicy defines how the \"rules\" list is used to match incoming requests.\nAllowed values are \"Exact\" or \"Equivalent\"."}, "namespaceSelector": {"description": "NamespaceSelector decides whether to run the webhook on an object based\non whether the namespace for that object matches the selector. If the\nobject itself is a namespace, the matching is performed on\nobject.metadata.labels. If the object is another cluster scoped resource,\nit never skips the webhook."}, "objectSelector": {"description": "ObjectSelector decides whether to run the webhook based on if the\nobject has matching labels. objectSelector is evaluated against both\nthe oldObject and newObject that would be sent to the webhook, and\nis considered to match if either object matches the selector."}, "sideEffects": {"type": "string", "description": "SideEffects states whether this webhook has side effects."}, "timeoutSeconds": {"type": "integer", "description": "TimeoutSeconds specifies the timeout for this webhook."}, "admissionReviewVersions": {"items": {"type": "string"}, "type": "array", "description": "AdmissionReviewVersions is an ordered list of preferred `AdmissionReview`\nversions the Webhook expects."}, "matchConditions": {"items": true, "type": "array", "description": "MatchConditions is a list of conditions that must be met for a request to be sent to this\nwebhook. Match conditions filter requests that have already been matched by the rules,\nnamespaceSelector, and objectSelector. An empty list of matchConditions matches all requests.\nThere are a maximum of 64 match conditions allowed."}}, "additionalProperties": false, "type": "object"}, "MutatingWebhookConfiguration": {"properties": {"kind": {"type": "string", "description": "Kind is a string value representing the REST resource this object represents.\nServers may infer this from the endpoint the client submits requests to."}, "apiVersion": {"type": "string", "description": "APIVersion defines the versioned schema of this representation of an object.\nServers should convert recognized schemas to the latest internal value, and\nmay reject unrecognized values."}, "metadata": {"$ref": "#/$defs/ObjectMeta", "description": "Standard object metadata; More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#metadata."}, "webhooks": {"items": {"$ref": "#/$defs/MutatingWebhook"}, "type": "array", "description": "Webhooks is a list of webhooks and the affected resources and operations."}}, "additionalProperties": false, "type": "object"}, "NetworkPolicy": {"properties": {"enabled": {"type": "boolean", "description": "Enabled defines if the network policy should be deployed by vCluster."}, "fallbackDns": {"type": "string", "description": "FallbackDNS is the fallback DNS server to use if the virtual cluster does not have a DNS server."}, "outgoingConnections": {"$ref": "#/$defs/OutgoingConnections", "description": "OutgoingConnections are the outgoing connections options for the vCluster workloads."}, "extraControlPlaneRules": {"items": {"type": "object"}, "type": "array", "description": "ExtraControlPlaneRules are extra allowed rules for the vCluster control plane."}, "extraWorkloadRules": {"items": {"type": "object"}, "type": "array", "description": "ExtraWorkloadRules are extra allowed rules for the vCluster workloads."}, "annotations": {"additionalProperties": {"type": "string"}, "type": "object", "description": "Annotations are extra annotations for this resource."}, "labels": {"additionalProperties": {"type": "string"}, "type": "object", "description": "Labels are extra labels for this resource."}}, "additionalProperties": false, "type": "object"}, "NetworkProxyKubelets": {"properties": {"byHostname": {"type": "boolean", "description": "ByHostname will add a special vCluster hostname to the nodes where the node can be reached at. This doesn't work\nfor all applications, e.g. Prometheus requires a node IP."}, "byIP": {"type": "boolean", "description": "ByIP will create a separate service in the host cluster for every node that will point to virtual cluster and will be used to\nroute traffic."}}, "additionalProperties": false, "type": "object"}, "Networking": {"properties": {"serviceCIDR": {"type": "string", "description": "ServiceCIDR holds the service cidr for the virtual cluster. This should only be set if privateNodes.enabled is true or vCluster cannot detect the host service cidr."}, "podCIDR": {"type": "string", "description": "PodCIDR holds the pod cidr for the virtual cluster. This should only be set if privateNodes.enabled is true."}, "replicateServices": {"$ref": "#/$defs/ReplicateServices", "description": "ReplicateServices allows replicating services from the host within the virtual cluster or the other way around."}, "resolveDNS": {"items": {"$ref": "#/$defs/ResolveDNS"}, "type": "array", "description": "ResolveDNS allows to define extra DNS rules. This only works if embedded coredns is configured.", "pro": true}, "advanced": {"$ref": "#/$defs/NetworkingAdvanced", "description": "Advanced holds advanced network options."}}, "additionalProperties": false, "type": "object"}, "NetworkingAdvanced": {"properties": {"clusterDomain": {"type": "string", "description": "ClusterDomain is the Kubernetes cluster domain to use within the virtual cluster."}, "fallbackHostCluster": {"type": "boolean", "description": "FallbackHostCluster allows to fallback dns to the host cluster. This is useful if you want to reach host services without\nany other modification. You will need to provide a namespace for the service, e.g. my-other-service.my-other-namespace"}, "proxyKubelets": {"$ref": "#/$defs/NetworkProxyKubelets", "description": "ProxyKubelets allows rewriting certain metrics and stats from the Kubelet to \"fake\" this for applications such as\nprometheus or other node exporters."}}, "additionalProperties": false, "type": "object"}, "NodeRegistration": {"properties": {"criSocket": {"type": "string", "description": "CRI socket is the socket for the CRI."}, "kubeletExtraArgs": {"items": {"$ref": "#/$defs/KubeletExtraArg"}, "type": "array", "description": "KubeletExtraArgs passes through extra arguments to the kubelet. The arguments here are passed to the kubelet command line via the environment file\nkubeadm writes at runtime for the kubelet to source. This overrides the generic base-level configuration in the kubelet-config ConfigMap\nFlags have higher priority when parsing. These values are local and specific to the node kube<PERSON><PERSON> is executing on.\nAn argument name in this list is the flag name as it appears on the command line except without leading dash(es).\nExtra arguments will override existing default arguments. Duplicate extra arguments are allowed."}, "taints": {"items": {"$ref": "#/$defs/KubeletJoinTaint"}, "type": "array", "description": "Taints are additional taints to set for the kubelet."}, "ignorePreflightErrors": {"items": {"type": "string"}, "type": "array", "description": "IgnorePreflightErrors provides a slice of pre-flight errors to be ignored when the current node is registered, e.g. 'IsPrivilegedUser,Swap'.\nValue 'all' ignores errors from all checks."}, "imagePullPolicy": {"type": "string", "description": "ImagePullPolicy specifies the policy for image pulling during kubeadm \"init\" and \"join\" operations.\nThe value of this field must be one of \"Always\", \"IfNotPresent\" or \"Never\".\nIf this field is unset kubeadm will default it to \"IfNotPresent\", or pull the required images if not present on the host."}}, "additionalProperties": false, "type": "object"}, "ObjectMeta": {"properties": {"name": {"type": "string", "description": "Name must be unique within a namespace. Is required when creating resources, although\nsome resources may allow a client to request the generation of an appropriate name\nautomatically. Name is primarily intended for creation idempotence and configuration\ndefinition."}, "labels": {"additionalProperties": {"type": "string"}, "type": "object", "description": "Map of string keys and values that can be used to organize and categorize\n(scope and select) objects. May match selectors of replication controllers\nand services."}, "annotations": {"additionalProperties": {"type": "string"}, "type": "object", "description": "Annotations is an unstructured key value map stored with a resource that may be\nset by external tools to store and retrieve arbitrary metadata."}}, "additionalProperties": false, "type": "object"}, "OutgoingConnections": {"properties": {"ipBlock": {"$ref": "#/$defs/IPBlock", "description": "IPBlock describes a particular CIDR (Ex. \"***********/24\",\"2001:db8::/64\") that is allowed\nto the pods matched by a NetworkPolicySpec's podSelector. The except entry describes CIDRs\nthat should not be included within this rule."}, "platform": {"type": "boolean", "description": "Platform enables egress access towards loft platform"}}, "additionalProperties": false, "type": "object"}, "PlatformAPIKey": {"properties": {"secretName": {"type": "string", "description": "SecretName is the name of the secret where the platform access key is stored. This defaults to vcluster-platform-api-key if undefined."}, "namespace": {"type": "string", "description": "Namespace defines the namespace where the access key secret should be retrieved from. If this is not equal to the namespace\nwhere the vCluster instance is deployed, you need to make sure vCluster has access to this other namespace."}, "createRBAC": {"type": "boolean", "description": "CreateRBAC will automatically create the necessary RBAC roles and role bindings to allow vCluster to read the secret specified\nin the above namespace, if specified.\nThis defaults to true."}}, "additionalProperties": false, "type": "object", "description": "PlatformAPIKey defines where to find the platform access key."}, "PlatformConfig": {"properties": {"apiKey": {"$ref": "#/$defs/PlatformAPIKey", "description": "APIKey defines where to find the platform access key and host. By default, vCluster will search in the following locations in this precedence:\n* environment variable called LICENSE\n* secret specified under external.platform.apiKey.secretName\n* secret called \"vcluster-platform-api-key\" in the vCluster namespace"}, "project": {"type": "string", "description": "Project specifies which platform project the vcluster should be imported to"}}, "type": "object", "description": "PlatformConfig holds platform configuration"}, "Plugin": {"properties": {"name": {"type": "string", "description": "Name is the name of the init-container and NOT the plugin name"}, "image": {"type": "string", "description": "Image is the container image that should be used for the plugin"}, "imagePullPolicy": {"type": "string", "description": "ImagePullPolicy is the pull policy to use for the container image"}, "config": {"type": "object", "description": "Config is the plugin config to use. This can be arbitrary config used for the plugin."}, "rbac": {"$ref": "#/$defs/PluginsRBAC", "description": "RBAC holds additional rbac configuration for the plugin"}, "command": {"items": {"type": "string"}, "type": "array", "description": "Command is the command that should be used for the init container"}, "args": {"items": {"type": "string"}, "type": "array", "description": "Args are the arguments that should be used for the init container"}, "securityContext": {"type": "object", "description": "SecurityContext is the container security context used for the init container"}, "resources": {"type": "object", "description": "Resources are the container resources used for the init container"}, "volumeMounts": {"items": true, "type": "array", "description": "VolumeMounts are extra volume mounts for the init container"}, "version": {"type": "string", "description": "Version is the plugin version, this is only needed for legacy plugins."}, "env": {"items": true, "type": "array"}, "envFrom": {"items": true, "type": "array"}, "lifecycle": {"type": "object"}, "livenessProbe": {"type": "object"}, "readinessProbe": {"type": "object"}, "startupProbe": {"type": "object"}, "workingDir": {"type": "string"}, "optional": {"type": "boolean"}}, "additionalProperties": false, "type": "object"}, "Plugins": {"properties": {"name": {"type": "string", "description": "Name is the name of the init-container and NOT the plugin name"}, "image": {"type": "string", "description": "Image is the container image that should be used for the plugin"}, "imagePullPolicy": {"type": "string", "description": "ImagePullPolicy is the pull policy to use for the container image"}, "config": {"type": "object", "description": "Config is the plugin config to use. This can be arbitrary config used for the plugin."}, "rbac": {"$ref": "#/$defs/PluginsRBAC", "description": "RBAC holds additional rbac configuration for the plugin"}, "command": {"items": {"type": "string"}, "type": "array", "description": "Command is the command that should be used for the init container"}, "args": {"items": {"type": "string"}, "type": "array", "description": "Args are the arguments that should be used for the init container"}, "securityContext": {"type": "object", "description": "SecurityContext is the container security context used for the init container"}, "resources": {"type": "object", "description": "Resources are the container resources used for the init container"}, "volumeMounts": {"items": true, "type": "array", "description": "VolumeMounts are extra volume mounts for the init container"}}, "additionalProperties": false, "type": "object"}, "PluginsExtraRules": {"properties": {"extraRules": {"items": {"$ref": "#/$defs/RBACPolicyRule"}, "type": "array", "description": "ExtraRules are extra rbac permissions roles that will be added to role or cluster role"}}, "additionalProperties": false, "type": "object"}, "PluginsRBAC": {"properties": {"role": {"$ref": "#/$defs/PluginsExtraRules", "description": "Role holds extra virtual cluster role permissions for the plugin"}, "clusterRole": {"$ref": "#/$defs/PluginsExtraRules", "description": "ClusterRole holds extra virtual cluster cluster role permissions required for the plugin"}}, "additionalProperties": false, "type": "object"}, "PodDNSConfig": {"properties": {"nameservers": {"items": {"type": "string"}, "type": "array", "description": "A list of DNS name server IP addresses.\nThis will be appended to the base nameservers generated from DNSPolicy.\nDuplicated nameservers will be removed.\n+optional\n+listType=atomic"}, "searches": {"items": {"type": "string"}, "type": "array", "description": "A list of DNS search domains for host-name lookup.\nThis will be appended to the base search paths generated from DNSPolicy.\nDuplicated search paths will be removed.\n+optional\n+listType=atomic"}, "options": {"items": {"$ref": "#/$defs/PodDNSConfigOption"}, "type": "array", "description": "A list of DNS resolver options.\nThis will be merged with the base options generated from DNSPolicy.\nDuplicated entries will be removed. Resolution options given in Options\nwill override those that appear in the base DNSPolicy.\n+optional\n+listType=atomic"}}, "additionalProperties": false, "type": "object", "description": "PodDNSConfig defines the DNS parameters of a pod in addition to those generated from DNSPolicy."}, "PodDNSConfigOption": {"properties": {"name": {"type": "string", "description": "Required."}, "value": {"type": "string", "description": "+optional"}}, "additionalProperties": false, "type": "object", "description": "PodDNSConfigOption defines DNS resolver options of a pod."}, "Policies": {"properties": {"networkPolicy": {"$ref": "#/$defs/NetworkPolicy", "description": "NetworkPolicy specifies network policy options."}, "podSecurityStandard": {"type": "string", "description": "PodSecurityStandard that can be enforced can be one of: empty (\"\"), baseline, restricted or privileged"}, "resourceQuota": {"$ref": "#/$defs/ResourceQuota", "description": "ResourceQuota specifies resource quota options."}, "limitRange": {"$ref": "#/$defs/LimitRange", "description": "LimitRange specifies limit range options."}, "centralAdmission": {"$ref": "#/$defs/CentralAdmission", "description": "CentralAdmission defines what validating or mutating webhooks should be enforced within the virtual cluster.", "pro": true}}, "additionalProperties": false, "type": "object"}, "PrivateNodes": {"properties": {"enabled": {"type": "boolean", "description": "Enabled defines if dedicated nodes should be enabled."}, "kubelet": {"$ref": "#/$defs/Kubelet", "description": "Kubelet holds kubelet configuration that is used for all nodes."}, "autoUpgrade": {"$ref": "#/$defs/AutoUpgrade", "description": "AutoUpgrade holds configuration for auto upgrade."}, "joinNode": {"$ref": "#/$defs/JoinConfiguration", "description": "JoinNode holds configuration specifically used during joining the node (see \"kubeadm join\")."}, "autoNodes": {"items": {"$ref": "#/$defs/PrivateNodesAutoNodes"}, "type": "array", "description": "AutoNodes stores auto nodes configuration."}, "vpn": {"$ref": "#/$defs/PrivateNodesVPN", "description": "VPN holds configuration for the private nodes vpn. This can be used to connect the private nodes to the control plane or\nconnect the private nodes to each other if they are not running in the same network. Platform connection is required for the vpn to work."}}, "additionalProperties": false, "type": "object", "description": "PrivateNodes enables private nodes for vCluster."}, "PrivateNodesAutoNodes": {"properties": {"provider": {"type": "string", "description": "Provider is the node provider of the nodes in this pool."}, "properties": {"additionalProperties": {"type": "string"}, "type": "object", "description": "Properties are the node provider properties. This is a simple key value map and can contain things\nlike region, subscription, etc. that is then used by the node provider to create the nodes and node environment."}, "static": {"items": {"$ref": "#/$defs/StaticNodePool"}, "type": "array", "description": "Static defines static node pools. Static node pools have a fixed size and are not scaled automatically."}, "dynamic": {"items": {"$ref": "#/$defs/DynamicNodePool"}, "type": "array", "description": "Dynamic defines dynamic node pools. Dynamic node pools are scaled automatically based on the requirements within the cluster.\nKarpenter is used under the hood to handle the scheduling of the nodes."}}, "additionalProperties": false, "type": "object", "required": ["provider"], "description": "PrivateNodesAutoNodes defines auto nodes"}, "PrivateNodesVPN": {"properties": {"enabled": {"type": "boolean", "description": "Enabled defines if the private nodes vpn should be enabled."}, "nodeToNode": {"$ref": "#/$defs/PrivateNodesVPNNodeToNode", "description": "NodeToNode holds configuration for the node to node vpn. This can be used to connect the private nodes to each other if they are not running in the same network."}}, "additionalProperties": false, "type": "object"}, "PrivateNodesVPNNodeToNode": {"properties": {"enabled": {"type": "boolean", "description": "Enabled defines if the node to node vpn should be enabled."}}, "additionalProperties": false, "type": "object"}, "RBAC": {"properties": {"role": {"$ref": "#/$defs/RBACRole", "description": "Role holds virtual cluster role configuration"}, "clusterRole": {"$ref": "#/$defs/RBACClusterRole", "description": "ClusterRole holds virtual cluster cluster role configuration"}, "enableVolumeSnapshotRules": {"$ref": "#/$defs/EnableAutoSwitch", "description": "EnableVolumeSnapshotRules enables all required volume snapshot rules in the Role and\nClusterRole."}}, "additionalProperties": false, "type": "object"}, "RBACClusterRole": {"properties": {"enabled": {"oneOf": [{"type": "string"}, {"type": "boolean"}], "description": "Enabled defines if the cluster role should be enabled or disabled. If auto, vCluster automatically determines whether the virtual cluster requires a cluster role."}, "extraRules": {"items": {"type": "object"}, "type": "array", "description": "ExtraRules will add rules to the cluster role."}, "overwriteRules": {"items": {"type": "object"}, "type": "array", "description": "OverwriteRules will overwrite the cluster role rules completely."}}, "additionalProperties": false, "type": "object"}, "RBACPolicyRule": {"properties": {"verbs": {"items": {"type": "string"}, "type": "array", "description": "Verbs is a list of Verbs that apply to ALL the ResourceKinds contained in this rule. '*' represents all verbs."}, "apiGroups": {"items": {"type": "string"}, "type": "array", "description": "APIGroups is the name of the APIGroup that contains the resources.  If multiple API groups are specified, any action requested against one of\nthe enumerated resources in any API group will be allowed. \"\" represents the core API group and \"*\" represents all API groups."}, "resources": {"items": {"type": "string"}, "type": "array", "description": "Resources is a list of resources this rule applies to. '*' represents all resources."}, "resourceNames": {"items": {"type": "string"}, "type": "array", "description": "ResourceNames is an optional white list of names that the rule applies to.  An empty set means that everything is allowed."}, "nonResourceURLs": {"items": {"type": "string"}, "type": "array", "description": "NonResourceURLs is a set of partial urls that a user should have access to.  *s are allowed, but only as the full, final step in the path\nSince non-resource URLs are not namespaced, this field is only applicable for ClusterRoles referenced from a ClusterRoleBinding.\nRules can either apply to API resources (such as \"pods\" or \"secrets\") or non-resource URL paths (such as \"/api\"),  but not both."}}, "additionalProperties": false, "type": "object"}, "RBACRole": {"properties": {"enabled": {"type": "boolean", "description": "Enabled defines if the role should be enabled or disabled."}, "extraRules": {"items": {"type": "object"}, "type": "array", "description": "ExtraRules will add rules to the role."}, "overwriteRules": {"items": {"type": "object"}, "type": "array", "description": "OverwriteRules will overwrite the role rules completely."}}, "additionalProperties": false, "type": "object"}, "ReadinessProbe": {"properties": {"enabled": {"type": "boolean", "description": "Enabled defines if this option should be enabled."}, "failureThreshold": {"type": "integer", "description": "Number of consecutive failures for the probe to be considered failed"}, "timeoutSeconds": {"type": "integer", "description": "Maximum duration (in seconds) that the probe will wait for a response."}, "periodSeconds": {"type": "integer", "description": "Frequency (in seconds) to perform the probe"}}, "additionalProperties": false, "type": "object", "description": "ReadinessProbe defines the configuration for the readiness probe."}, "Registry": {"properties": {"enabled": {"type": "boolean", "description": "Enabled defines if the embedded registry should be enabled."}, "anonymousPull": {"type": "boolean", "description": "AnonymousPull allows enabling anonymous pull for the embedded registry. This allows anybody to pull images from the registry without authentication."}, "config": {"description": "Config is the regular docker registry config. See https://distribution.github.io/distribution/about/configuration/ for more details."}}, "additionalProperties": false, "type": "object"}, "ReplicateServices": {"properties": {"toHost": {"items": {"$ref": "#/$defs/ServiceMapping"}, "type": "array", "description": "ToHost defines the services that should get synced from virtual cluster to the host cluster. If services are\nsynced to a different namespace than the virtual cluster is in, additional permissions for the other namespace\nare required."}, "fromHost": {"items": {"$ref": "#/$defs/ServiceMapping"}, "type": "array", "description": "FromHost defines the services that should get synced from the host to the virtual cluster."}}, "additionalProperties": false, "type": "object"}, "Requirement": {"properties": {"property": {"type": "string", "description": "Property is the property on the node type to select."}, "operator": {"type": "string", "description": "Operator is the comparison operator, such as \"In\", \"NotIn\", \"Exists\". If empty, defaults to \"In\"."}, "values": {"items": {"type": "string"}, "type": "array", "description": "Values is the list of values to use for comparison. This is mutually exclusive with value."}, "value": {"type": "string", "description": "Value is the value to use for comparison. This is mutually exclusive with values."}}, "additionalProperties": false, "type": "object", "required": ["property"], "description": "KarpenterRequirement defines a scheduling requirement for a dynamic node pool."}, "ResolveDNS": {"properties": {"hostname": {"type": "string", "description": "Hostname is the hostname within the vCluster that should be resolved from."}, "service": {"type": "string", "description": "Service is the virtual cluster service that should be resolved from."}, "namespace": {"type": "string", "description": "Namespace is the virtual cluster namespace that should be resolved from."}, "target": {"$ref": "#/$defs/ResolveDNSTarget", "description": "Target is the DNS target that should get mapped to"}}, "additionalProperties": false, "type": "object"}, "ResolveDNSTarget": {"properties": {"hostname": {"type": "string", "description": "Hostname to use as a DNS target"}, "ip": {"type": "string", "description": "IP to use as a DNS target"}, "hostService": {"type": "string", "description": "HostService to target, format is hostNamespace/hostService"}, "hostNamespace": {"type": "string", "description": "HostNamespace to target"}, "vClusterService": {"type": "string", "description": "VClusterService format is hostNamespace/vClusterName/vClusterNamespace/vClusterService"}}, "additionalProperties": false, "type": "object"}, "ResourceQuota": {"properties": {"enabled": {"oneOf": [{"type": "string"}, {"type": "boolean"}], "description": "Enabled defines if the resource quota should be enabled. \"auto\" means that if limitRange is enabled,\nthe resourceQuota will be enabled as well."}, "quota": {"type": "object", "description": "Quota are the quota options"}, "scopeSelector": {"type": "object", "description": "ScopeSelector is the resource quota scope selector"}, "scopes": {"items": {"type": "string"}, "type": "array", "description": "Scopes are the resource quota scopes"}, "annotations": {"additionalProperties": {"type": "string"}, "type": "object", "description": "Annotations are extra annotations for this resource."}, "labels": {"additionalProperties": {"type": "string"}, "type": "object", "description": "Labels are extra labels for this resource."}}, "additionalProperties": false, "type": "object"}, "Resources": {"properties": {"limits": {"type": "object", "description": "Limits are resource limits for the container"}, "requests": {"type": "object", "description": "Requests are minimal resources that will be consumed by the container"}}, "additionalProperties": false, "type": "object"}, "RuleWithVerbs": {"properties": {"apiGroups": {"items": {"type": "string"}, "type": "array", "description": "APIGroups is the API groups the resources belong to. '*' is all groups."}, "apiVersions": {"items": {"type": "string"}, "type": "array", "description": "APIVersions is the API versions the resources belong to. '*' is all versions."}, "resources": {"items": {"type": "string"}, "type": "array", "description": "Resources is a list of resources this rule applies to."}, "scope": {"type": "string", "description": "<PERSON><PERSON> specifies the scope of this rule."}, "operations": {"items": {"type": "string"}, "type": "array", "description": "Verb is the kube verb associated with the request for API requests, not the http verb. This includes things like list and watch.\nFor non-resource requests, this is the lowercase http verb.\nIf '*' is present, the length of the slice must be one."}}, "additionalProperties": false, "type": "object"}, "SelectorConfig": {"properties": {"selector": {"$ref": "#/$defs/StandardLabelSelector"}}, "additionalProperties": false, "type": "object"}, "ServiceMapping": {"properties": {"from": {"type": "string", "description": "From is the service that should get synced. Can be either in the form name or namespace/name."}, "to": {"type": "string", "description": "To is the target service that it should get synced to. Can be either in the form name or namespace/name."}}, "additionalProperties": false, "type": "object"}, "ServiceMonitor": {"properties": {"enabled": {"type": "boolean", "description": "Enabled configures if <PERSON><PERSON> should create the service monitor."}, "labels": {"additionalProperties": {"type": "string"}, "type": "object", "description": "Labels are the extra labels to add to the service monitor."}, "annotations": {"additionalProperties": {"type": "string"}, "type": "object", "description": "Annotations are the extra annotations to add to the service monitor."}}, "additionalProperties": false, "type": "object"}, "SleepMode": {"properties": {"enabled": {"type": "boolean", "description": "Enabled toggles the sleep mode functionality, allowing for disabling sleep mode without removing other config"}, "timeZone": {"type": "string", "description": "Timezone represents the timezone a sleep schedule should run against, defaulting to UTC if unset"}, "autoSleep": {"$ref": "#/$defs/SleepModeAutoSleep", "description": "AutoSleep holds autoSleep details"}, "autoWakeup": {"$ref": "#/$defs/AutoWakeup", "description": "AutoWakeup holds configuration for waking the vCluster on a schedule rather than waiting for some activity."}}, "additionalProperties": false, "type": "object", "description": "SleepMode holds configuration for native/workload only sleep mode"}, "SleepModeAutoSleep": {"properties": {"afterInactivity": {"type": "string", "description": "AfterInactivity represents how long a vCluster can be idle before workloads are automaticaly put to sleep"}, "schedule": {"type": "string", "description": "Schedule represents a cron schedule for when to sleep workloads"}, "exclude": {"$ref": "#/$defs/AutoSleepExclusion", "description": "Exclude holds configuration for labels that, if present, will prevent a workload from going to sleep"}}, "additionalProperties": false, "type": "object", "description": "SleepModeAutoSleep holds configuration for allowing a vCluster to sleep its workloads automatically"}, "Standalone": {"properties": {"enabled": {"type": "boolean", "description": "Enabled defines if standalone mode should be enabled."}, "dataDir": {"type": "string", "description": "DataDir defines the data directory for the standalone mode."}, "autoNodes": {"$ref": "#/$defs/StandaloneAutoNodes", "description": "AutoNodes automatically deploys nodes for standalone mode."}, "joinNode": {"$ref": "#/$defs/StandaloneJoinNode", "description": "JoinNode holds configuration for the standalone control plane node."}}, "additionalProperties": false, "type": "object"}, "StandaloneAutoNodes": {"properties": {"provider": {"type": "string", "description": "Provider is the node provider of the nodes in this pool."}, "quantity": {"type": "integer", "description": "Quantity is the number of nodes to deploy for standalone mode."}, "nodeTypeSelector": {"items": {"$ref": "#/$defs/Requirement"}, "type": "array", "description": "NodeTypeSelector filters the types of nodes that can be provisioned by this pool.\nAll requirements must be met for a node type to be eligible."}}, "additionalProperties": false, "type": "object"}, "StandaloneJoinNode": {"properties": {"enabled": {"type": "boolean", "description": "Enabled defines if the standalone node should be joined into the cluster. If false, only the control plane binaries will be executed and no node will show up in the actual cluster."}, "preInstallCommands": {"items": {"type": "string"}, "type": "array", "description": "PreInstallCommands are commands that will be executed before containerd, kubelet etc. is installed."}, "preJoinCommands": {"items": {"type": "string"}, "type": "array", "description": "PreJoinCommands are commands that will be executed before kubeadm join is executed."}, "postJoinCommands": {"items": {"type": "string"}, "type": "array", "description": "PostJoinCommands are commands that will be executed after kubeadm join is executed."}, "containerd": {"$ref": "#/$defs/ContainerdJoin", "description": "Containerd holds configuration for the containerd join process."}, "caCertPath": {"type": "string", "description": "CACertPath is the path to the SSL certificate authority used to\nsecure communications between node and control-plane.\nDefaults to \"/etc/kubernetes/pki/ca.crt\"."}, "skipPhases": {"items": {"type": "string"}, "type": "array", "description": "SkipPhases is a list of phases to skip during command execution.\nThe list of phases can be obtained with the \"kubeadm join --help\" command."}, "nodeRegistration": {"$ref": "#/$defs/NodeRegistration", "description": "NodeRegistration holds configuration for the node registration similar to the kubeadm node registration."}}, "additionalProperties": false, "type": "object"}, "StandardLabelSelector": {"properties": {"matchLabels": {"additionalProperties": {"type": "string"}, "type": "object"}, "matchExpressions": {"items": {"$ref": "#/$defs/LabelSelectorRequirement"}, "type": "array"}}, "additionalProperties": false, "type": "object"}, "StartupProbe": {"properties": {"enabled": {"type": "boolean", "description": "Enabled defines if this option should be enabled."}, "failureThreshold": {"type": "integer", "description": "Number of consecutive failures allowed before failing the pod"}, "timeoutSeconds": {"type": "integer", "description": "Maximum duration (in seconds) that the probe will wait for a response."}, "periodSeconds": {"type": "integer", "description": "Frequency (in seconds) to perform the probe"}}, "additionalProperties": false, "type": "object", "description": "StartupProbe defines the configuration for the startup probe."}, "StaticNodePool": {"properties": {"name": {"type": "string", "description": "Name is the name of this static nodePool"}, "nodeTypeSelector": {"items": {"$ref": "#/$defs/Requirement"}, "type": "array", "description": "NodeTypeSelector filters the types of nodes that can be provisioned by this pool.\nAll requirements must be met for a node type to be eligible."}, "taints": {"items": {"$ref": "#/$defs/KubeletJoinTaint"}, "type": "array", "description": "Taints are the taints to apply to the nodes in this pool."}, "nodeLabels": {"additionalProperties": {"type": "string"}, "type": "object", "description": "NodeLabels are the labels to apply to the nodes in this pool."}, "terminationGracePeriod": {"type": "string", "description": "TerminationGracePeriod is the maximum duration the controller will wait before forcefully deleting the pods on a node, measured from when deletion is first initiated.\n\nWarning: this feature takes precedence over a Pod's terminationGracePeriodSeconds value, and bypasses any blocked PDBs or the karpenter.sh/do-not-disrupt annotation.\n\nThis field is intended to be used by cluster administrators to enforce that nodes can be cycled within a given time period.\nWhen set, drifted nodes will begin draining even if there are pods blocking eviction. Draining will respect PDBs and the do-not-disrupt annotation until the TGP is reached.\n\nKarpenter will preemptively delete pods so their terminationGracePeriodSeconds align with the node's terminationGracePeriod.\nIf a pod would be terminated without being granted its full terminationGracePeriodSeconds prior to the node timeout,\nthat pod will be deleted at T = node timeout - pod terminationGracePeriodSeconds.\n\nThe feature can also be used to allow maximum time limits for long-running jobs which can delay node termination with preStop hooks.\nDefaults to 30s. Set to Never to wait indefinitely for pods to be drained."}, "quantity": {"type": "integer", "description": "Quantity is the number of desired nodes in this pool."}}, "additionalProperties": false, "type": "object", "required": ["name", "quantity"]}, "Sync": {"properties": {"toHost": {"$ref": "#/$defs/SyncToHost", "description": "Configure resources to sync from the virtual cluster to the host cluster."}, "fromHost": {"$ref": "#/$defs/SyncFromHost", "description": "Configure what resources vCluster should sync from the host cluster to the virtual cluster."}}, "additionalProperties": false, "type": "object"}, "SyncAllResource": {"properties": {"enabled": {"type": "boolean", "description": "Enabled defines if this option should be enabled."}, "all": {"type": "boolean", "description": "All defines if all resources of that type should get synced or only the necessary ones that are needed."}, "patches": {"items": {"$ref": "#/$defs/TranslatePatch"}, "type": "array", "description": "Patches patch the resource according to the provided specification."}}, "additionalProperties": false, "type": "object"}, "SyncFromHost": {"properties": {"nodes": {"$ref": "#/$defs/SyncNodes", "description": "Nodes defines if nodes should get synced from the host cluster to the virtual cluster, but not back."}, "events": {"$ref": "#/$defs/EnableSwitchWithPatches", "description": "Events defines if events should get synced from the host cluster to the virtual cluster, but not back."}, "ingressClasses": {"$ref": "#/$defs/EnableSwitchWithPatchesAndSelector", "description": "IngressClasses defines if ingress classes should get synced from the host cluster to the virtual cluster, but not back."}, "runtimeClasses": {"$ref": "#/$defs/EnableSwitchWithPatchesAndSelector", "description": "RuntimeClasses defines if runtime classes should get synced from the host cluster to the virtual cluster, but not back."}, "priorityClasses": {"$ref": "#/$defs/EnableSwitchWithPatchesAndSelector", "description": "PriorityClasses defines if priority classes classes should get synced from the host cluster to the virtual cluster, but not back."}, "storageClasses": {"$ref": "#/$defs/EnableAutoSwitchWithPatchesAndSelector", "description": "StorageClasses defines if storage classes should get synced from the host cluster to the virtual cluster, but not back. If auto, is automatically enabled when the virtual scheduler is enabled."}, "csiNodes": {"$ref": "#/$defs/EnableAutoSwitchWithPatches", "description": "CSINodes defines if csi nodes should get synced from the host cluster to the virtual cluster, but not back. If auto, is automatically enabled when the virtual scheduler is enabled."}, "csiDrivers": {"$ref": "#/$defs/EnableAutoSwitchWithPatches", "description": "CSIDrivers defines if csi drivers should get synced from the host cluster to the virtual cluster, but not back. If auto, is automatically enabled when the virtual scheduler is enabled."}, "csiStorageCapacities": {"$ref": "#/$defs/EnableAutoSwitchWithPatches", "description": "CSIStorageCapacities defines if csi storage capacities should get synced from the host cluster to the virtual cluster, but not back. If auto, is automatically enabled when the virtual scheduler is enabled."}, "customResources": {"additionalProperties": {"$ref": "#/$defs/SyncFromHostCustomResource"}, "type": "object", "description": "CustomResources defines what custom resources should get synced read-only to the virtual cluster from the host cluster. vCluster will automatically add any required RBAC to the vCluster cluster role."}, "volumeSnapshotClasses": {"$ref": "#/$defs/EnableSwitchWithPatches", "description": "VolumeSnapshotClasses defines if volume snapshot classes created within the virtual cluster should get synced to the host cluster."}, "configMaps": {"$ref": "#/$defs/EnableSwitchWithResourcesMappings", "description": "ConfigMaps defines if config maps in the host should get synced to the virtual cluster."}, "secrets": {"$ref": "#/$defs/EnableSwitchWithResourcesMappings", "description": "Secrets defines if secrets in the host should get synced to the virtual cluster."}}, "additionalProperties": false, "type": "object"}, "SyncFromHostCustomResource": {"properties": {"enabled": {"type": "boolean", "description": "Enabled defines if this option should be enabled."}, "scope": {"type": "string", "description": "<PERSON><PERSON> defines the scope of the resource"}, "patches": {"items": {"$ref": "#/$defs/TranslatePatch"}, "type": "array", "description": "Patches patch the resource according to the provided specification."}, "mappings": {"$ref": "#/$defs/FromHostMappings", "description": "Mappings for Namespace and Object"}}, "additionalProperties": false, "type": "object", "required": ["enabled", "scope"]}, "SyncNodeSelector": {"properties": {"all": {"type": "boolean", "description": "All specifies if all nodes should get synced by vCluster from the host to the virtual cluster or only the ones where pods are assigned to."}, "labels": {"additionalProperties": {"type": "string"}, "type": "object", "description": "Labels are the node labels used to sync nodes from host cluster to virtual cluster. This will also set the node selector when syncing a pod from virtual cluster to host cluster to the same value."}}, "additionalProperties": false, "type": "object"}, "SyncNodes": {"properties": {"enabled": {"type": "boolean", "description": "Enabled specifies if syncing real nodes should be enabled. If this is disabled, vCluster will create fake nodes instead."}, "syncBackChanges": {"type": "boolean", "description": "SyncBackChanges enables syncing labels and taints from the virtual cluster to the host cluster. If this is enabled someone within the virtual cluster will be able to change the labels and taints of the host cluster node."}, "clearImageStatus": {"type": "boolean", "description": "ClearImageStatus will erase the image status when syncing a node. This allows to hide images that are pulled by the node."}, "selector": {"$ref": "#/$defs/SyncNodeSelector", "description": "Selector can be used to define more granular what nodes should get synced from the host cluster to the virtual cluster."}, "patches": {"items": {"$ref": "#/$defs/TranslatePatch"}, "type": "array", "description": "Patches patch the resource according to the provided specification."}}, "additionalProperties": false, "type": "object"}, "SyncPods": {"properties": {"enabled": {"type": "boolean", "description": "Enabled defines if pod syncing should be enabled."}, "translateImage": {"additionalProperties": {"type": "string"}, "type": "object", "description": "TranslateImage maps an image to another image that should be used instead. For example this can be used to rewrite\na certain image that is used within the virtual cluster to be another image on the host cluster"}, "enforceTolerations": {"items": {"type": "string"}, "type": "array", "description": "EnforceTolerations will add the specified tolerations to all pods synced by the virtual cluster."}, "useSecretsForSATokens": {"type": "boolean", "description": "UseSecretsForSATokens will use secrets to save the generated service account tokens by virtual cluster instead of using a\npod annotation."}, "runtimeClassName": {"type": "string", "description": "RuntimeClassName is the runtime class to set for synced pods."}, "priorityClassName": {"type": "string", "description": "PriorityClassName is the priority class to set for synced pods."}, "rewriteHosts": {"$ref": "#/$defs/SyncRewriteHosts", "description": "RewriteHosts is a special option needed to rewrite statefulset containers to allow the correct FQDN. virtual cluster will add\na small container to each stateful set pod that will initially rewrite the /etc/hosts file to match the FQDN expected by\nthe virtual cluster."}, "patches": {"items": {"$ref": "#/$defs/TranslatePatch"}, "type": "array", "description": "Patches patch the resource according to the provided specification."}, "hybridScheduling": {"$ref": "#/$defs/HybridScheduling", "description": "HybridScheduling is used to enable and configure hybrid scheduling for pods in the virtual cluster."}}, "additionalProperties": false, "type": "object"}, "SyncRewriteHosts": {"properties": {"enabled": {"type": "boolean", "description": "Enabled specifies if rewriting stateful set pods should be enabled."}, "initContainer": {"$ref": "#/$defs/SyncRewriteHostsInitContainer", "description": "InitContainer holds extra options for the init container used by vCluster to rewrite the FQDN for stateful set pods."}}, "additionalProperties": false, "type": "object"}, "SyncRewriteHostsInitContainer": {"properties": {"image": {"$ref": "#/$defs/Image", "description": "Image is the image virtual cluster should use to rewrite this FQDN."}, "resources": {"$ref": "#/$defs/Resources", "description": "Resources are the resources that should be assigned to the init container for each stateful set init container."}}, "additionalProperties": false, "type": "object"}, "SyncToHost": {"properties": {"pods": {"$ref": "#/$defs/SyncPods", "description": "Pods defines if pods created within the virtual cluster should get synced to the host cluster."}, "secrets": {"$ref": "#/$defs/SyncAllResource", "description": "Secrets defines if secrets created within the virtual cluster should get synced to the host cluster."}, "configMaps": {"$ref": "#/$defs/SyncAllResource", "description": "ConfigMaps defines if config maps created within the virtual cluster should get synced to the host cluster."}, "ingresses": {"$ref": "#/$defs/EnableSwitchWithPatches", "description": "Ingresses defines if ingresses created within the virtual cluster should get synced to the host cluster."}, "services": {"$ref": "#/$defs/EnableSwitchWithPatches", "description": "Services defines if services created within the virtual cluster should get synced to the host cluster."}, "endpoints": {"$ref": "#/$defs/EnableSwitchWithPatches", "description": "Endpoints defines if endpoints created within the virtual cluster should get synced to the host cluster."}, "endpointSlices": {"$ref": "#/$defs/EnableSwitchWithPatches", "description": "EndpointSlices defines if endpointslices created within the virtual cluster should get synced to the host cluster."}, "networkPolicies": {"$ref": "#/$defs/EnableSwitchWithPatches", "description": "NetworkPolicies defines if network policies created within the virtual cluster should get synced to the host cluster."}, "persistentVolumeClaims": {"$ref": "#/$defs/EnableSwitchWithPatches", "description": "PersistentVolumeClaims defines if persistent volume claims created within the virtual cluster should get synced to the host cluster."}, "persistentVolumes": {"$ref": "#/$defs/EnableSwitchWithPatches", "description": "PersistentVolumes defines if persistent volumes created within the virtual cluster should get synced to the host cluster."}, "volumeSnapshots": {"$ref": "#/$defs/EnableSwitchWithPatches", "description": "VolumeSnapshots defines if volume snapshots created within the virtual cluster should get synced to the host cluster."}, "volumeSnapshotContents": {"$ref": "#/$defs/EnableSwitchWithPatches", "description": "VolumeSnapshotContents defines if volume snapshot contents created within the virtual cluster should get synced to the host cluster."}, "storageClasses": {"$ref": "#/$defs/EnableSwitchWithPatches", "description": "StorageClasses defines if storage classes created within the virtual cluster should get synced to the host cluster."}, "serviceAccounts": {"$ref": "#/$defs/EnableSwitchWithPatches", "description": "ServiceAccounts defines if service accounts created within the virtual cluster should get synced to the host cluster."}, "podDisruptionBudgets": {"$ref": "#/$defs/EnableSwitchWithPatches", "description": "PodDisruptionBudgets defines if pod disruption budgets created within the virtual cluster should get synced to the host cluster."}, "priorityClasses": {"$ref": "#/$defs/EnableSwitchWithPatches", "description": "PriorityClasses defines if priority classes created within the virtual cluster should get synced to the host cluster."}, "customResources": {"additionalProperties": {"$ref": "#/$defs/SyncToHostCustomResource"}, "type": "object", "description": "CustomResources defines what custom resources should get synced from the virtual cluster to the host cluster. vCluster will copy the definition automatically from host cluster to virtual cluster on startup.\nvCluster will also automatically add any required RBAC permissions to the vCluster role for this to work."}, "namespaces": {"$ref": "#/$defs/SyncToHostNamespaces", "description": "Namespaces defines if namespaces created within the virtual cluster should get synced to the host cluster."}}, "additionalProperties": false, "type": "object"}, "SyncToHostCustomResource": {"properties": {"enabled": {"type": "boolean", "description": "Enabled defines if this option should be enabled."}, "scope": {"type": "string", "description": "<PERSON><PERSON> defines the scope of the resource. If undefined, will use Namespaced. Currently only Namespaced is supported."}, "patches": {"items": {"$ref": "#/$defs/TranslatePatch"}, "type": "array", "description": "Patches patch the resource according to the provided specification."}}, "additionalProperties": false, "type": "object", "required": ["enabled"]}, "SyncToHostNamespaces": {"properties": {"enabled": {"type": "boolean", "description": "Enabled defines if this option should be enabled."}, "patches": {"items": {"$ref": "#/$defs/TranslatePatch"}, "type": "array", "description": "Patches patch the resource according to the provided specification."}, "mappings": {"$ref": "#/$defs/FromHostMappings", "description": "Mappings for Namespace and Object"}, "mappingsOnly": {"type": "boolean", "description": "MappingsOnly defines if creation of namespaces not matched by mappings should be allowed."}, "extraLabels": {"additionalProperties": {"type": "string"}, "type": "object", "description": "ExtraLabels are additional labels to add to the namespace in the host cluster."}}, "additionalProperties": false, "type": "object", "required": ["enabled"], "description": "SyncToHostNamespaces defines how namespaces should be synced from the virtual cluster to the host cluster."}, "Telemetry": {"properties": {"enabled": {"type": "boolean", "description": "Enabled specifies that the telemetry for the vCluster control plane should be enabled."}, "instanceCreator": {"type": "string"}, "machineID": {"type": "string"}, "platformUserID": {"type": "string"}, "platformInstanceID": {"type": "string"}}, "additionalProperties": false, "type": "object"}, "TranslatePatch": {"properties": {"path": {"type": "string", "description": "Path is the path within the patch to target. If the path is not found within the patch, the patch is not applied."}, "expression": {"type": "string", "description": "Expression transforms the value according to the given JavaScript expression."}, "reverseExpression": {"type": "string", "description": "ReverseExpression transforms the value according to the given JavaScript expression."}, "reference": {"$ref": "#/$defs/TranslatePatchReference", "description": "Reference treats the path value as a reference to another object and will rewrite it based on the chosen mode\nautomatically. In single-namespace mode this will translate the name to \"vxxxxxxxxx\" to avoid conflicts with\nother names, in multi-namespace mode this will not translate the name."}, "labels": {"$ref": "#/$defs/TranslatePatchLabels", "description": "Labels treats the path value as a labels selector."}}, "additionalProperties": false, "type": "object", "required": ["path"]}, "TranslatePatchLabels": {"properties": {}, "additionalProperties": false, "type": "object"}, "TranslatePatchReference": {"properties": {"apiVersion": {"type": "string", "description": "APIVersion is the apiVersion of the referenced object."}, "apiVersionPath": {"type": "string", "description": "APIVersionPath is optional relative path to use to determine the kind. If APIVersionPath is not found, will fallback to apiVersion."}, "kind": {"type": "string", "description": "Kind is the kind of the referenced object."}, "kindPath": {"type": "string", "description": "KindPath is the optional relative path to use to determine the kind. If KindPath is not found, will fallback to kind."}, "namePath": {"type": "string", "description": "NamePath is the optional relative path to the reference name within the object."}, "namespacePath": {"type": "string", "description": "NamespacePath is the optional relative path to the reference namespace within the object. If omitted or not found, namespacePath equals to the\nmetadata.namespace path of the object."}}, "additionalProperties": false, "type": "object", "required": ["apiVersion", "kind"]}, "ValidatingWebhook": {"properties": {"name": {"type": "string", "description": "The name of the admission webhook.\nName should be fully qualified, e.g., imagepolicy.kubernetes.io, where\n\"imagepolicy\" is the name of the webhook, and kubernetes.io is the name\nof the organization."}, "clientConfig": {"$ref": "#/$defs/ValidatingWebhookClientConfig", "description": "ClientConfig defines how to communicate with the hook."}, "rules": {"items": true, "type": "array", "description": "Rules describes what operations on what resources/subresources the webhook cares about.\nThe webhook cares about an operation if it matches _any_ Rule."}, "failurePolicy": {"type": "string", "description": "FailurePolicy defines how unrecognized errors from the admission endpoint are handled -\nallowed values are Ignore or Fail. Defaults to Fail."}, "matchPolicy": {"type": "string", "description": "matchPolicy defines how the \"rules\" list is used to match incoming requests.\nAllowed values are \"Exact\" or \"Equivalent\"."}, "namespaceSelector": {"description": "NamespaceSelector decides whether to run the webhook on an object based\non whether the namespace for that object matches the selector. If the\nobject itself is a namespace, the matching is performed on\nobject.metadata.labels. If the object is another cluster scoped resource,\nit never skips the webhook."}, "objectSelector": {"description": "ObjectSelector decides whether to run the webhook based on if the\nobject has matching labels. objectSelector is evaluated against both\nthe oldObject and newObject that would be sent to the webhook, and\nis considered to match if either object matches the selector."}, "sideEffects": {"type": "string", "description": "SideEffects states whether this webhook has side effects."}, "timeoutSeconds": {"type": "integer", "description": "TimeoutSeconds specifies the timeout for this webhook."}, "admissionReviewVersions": {"items": {"type": "string"}, "type": "array", "description": "AdmissionReviewVersions is an ordered list of preferred `AdmissionReview`\nversions the Webhook expects."}, "matchConditions": {"items": true, "type": "array", "description": "MatchConditions is a list of conditions that must be met for a request to be sent to this\nwebhook. Match conditions filter requests that have already been matched by the rules,\nnamespaceSelector, and objectSelector. An empty list of matchConditions matches all requests.\nThere are a maximum of 64 match conditions allowed."}}, "additionalProperties": false, "type": "object"}, "ValidatingWebhookClientConfig": {"properties": {"url": {"type": "string", "description": "URL gives the location of the webhook, in standard URL form\n(`scheme://host:port/path`). Exactly one of `url` or `service`\nmust be specified."}, "service": {"$ref": "#/$defs/ValidatingWebhookServiceReference", "description": "Service is a reference to the service for this webhook. Either\n`service` or `url` must be specified.\n\nIf the webhook is running within the cluster, then you should use `service`."}, "caBundle": {"type": "string", "contentEncoding": "base64", "description": "CABundle is a PEM encoded CA bundle which will be used to validate the webhook's server certificate.\nIf unspecified, system trust roots on the apiserver are used."}}, "additionalProperties": false, "type": "object", "description": "ValidatingWebhookClientConfig contains the information to make a TLS connection with the webhook"}, "ValidatingWebhookConfiguration": {"properties": {"kind": {"type": "string", "description": "Kind is a string value representing the REST resource this object represents.\nServers may infer this from the endpoint the client submits requests to."}, "apiVersion": {"type": "string", "description": "APIVersion defines the versioned schema of this representation of an object.\nServers should convert recognized schemas to the latest internal value, and\nmay reject unrecognized values."}, "metadata": {"$ref": "#/$defs/ObjectMeta", "description": "Standard object metadata; More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#metadata."}, "webhooks": {"items": {"$ref": "#/$defs/ValidatingWebhook"}, "type": "array", "description": "Webhooks is a list of webhooks and the affected resources and operations."}}, "additionalProperties": false, "type": "object"}, "ValidatingWebhookServiceReference": {"properties": {"namespace": {"type": "string", "description": "Namespace is the namespace of the service."}, "name": {"type": "string", "description": "Name is the name of the service."}, "path": {"type": "string", "description": "Path is an optional URL path which will be sent in any request to\nthis service."}, "port": {"type": "integer", "description": "If specified, the port on the service that hosting webhook.\nDefault to 443 for backward compatibility.\n`port` should be a valid port number (1-65535, inclusive)."}}, "additionalProperties": false, "type": "object"}, "VirtualClusterKubeConfig": {"properties": {"kubeConfig": {"type": "string", "description": "KubeConfig is the virtual cluster kubeconfig path."}, "serverCAKey": {"type": "string", "description": "ServerCAKey is the server ca key path."}, "serverCACert": {"type": "string", "description": "ServerCAKey is the server ca cert path."}, "clientCACert": {"type": "string", "description": "ServerCAKey is the client ca cert path."}, "requestHeaderCACert": {"type": "string", "description": "RequestHeaderCACert is the request header ca cert path."}}, "additionalProperties": false, "type": "object"}, "VolumeClaim": {"properties": {"enabled": {"oneOf": [{"type": "string"}, {"type": "boolean"}], "description": "Enabled enables deploying a persistent volume claim. If auto, vCluster will automatically determine\nbased on the chosen distro and other options if this is required."}, "accessModes": {"items": {"type": "string"}, "type": "array", "description": "AccessModes are the persistent volume claim access modes."}, "retentionPolicy": {"type": "string", "description": "RetentionPolicy is the persistent volume claim retention policy."}, "size": {"type": "string", "description": "Size is the persistent volume claim storage size."}, "storageClass": {"type": "string", "description": "StorageClass is the persistent volume claim storage class."}}, "additionalProperties": false, "type": "object"}, "VolumeMount": {"properties": {"name": {"type": "string", "description": "This must match the Name of a Volume."}, "readOnly": {"type": "boolean", "description": "Mounted read-only if true, read-write otherwise (false or unspecified).\nDefaults to false."}, "mountPath": {"type": "string", "description": "Path within the container at which the volume should be mounted.  Must\nnot contain ':'."}, "subPath": {"type": "string", "description": "Path within the volume from which the container's volume should be mounted.\nDefaults to \"\" (volume's root)."}, "mountPropagation": {"type": "string", "description": "mountPropagation determines how mounts are propagated from the host\nto container and the other way around.\nWhen not set, MountPropagationNone is used.\nThis field is beta in 1.10."}, "subPathExpr": {"type": "string", "description": "Expanded path within the volume from which the container's volume should be mounted.\nBehaves similarly to SubPath but environment variable references $(VAR_NAME) are expanded using the container's environment.\nDefaults to \"\" (volume's root).\nSubPathExpr and SubPath are mutually exclusive."}}, "additionalProperties": false, "type": "object", "description": "VolumeMount describes a mounting of a Volume within a container."}, "VolumeSnapshotController": {"properties": {"enabled": {"type": "boolean", "description": "Enabled defines if the CSI volumes snapshot-controller should be enabled."}}, "additionalProperties": false, "type": "object", "description": "VolumeSnapshotController defines CSI volumes snapshot-controller configuration."}}, "properties": {"global": {"description": "Global values shared across all (sub)charts"}, "exportKubeConfig": {"$ref": "#/$defs/ExportKubeConfig", "description": "ExportKubeConfig describes how vCluster should export the vCluster kubeConfig file."}, "sync": {"$ref": "#/$defs/Sync", "description": "Sync describes how to sync resources from the virtual cluster to host cluster and back."}, "integrations": {"$ref": "#/$defs/Integrations", "description": "Integrations holds config for vCluster integrations with other operators or tools running on the host cluster"}, "deploy": {"$ref": "#/$defs/Deploy", "description": "Deploy holds configuration for the deployment of vCluster."}, "networking": {"$ref": "#/$defs/Networking", "description": "Networking options related to the virtual cluster."}, "policies": {"$ref": "#/$defs/Policies", "description": "Policies to enforce for the virtual cluster deployment as well as within the virtual cluster."}, "controlPlane": {"$ref": "#/$defs/ControlPlane", "description": "Configure vCluster's control plane components and deployment."}, "privateNodes": {"$ref": "#/$defs/PrivateNodes", "description": "PrivateNodes holds configuration for vCluster private nodes mode."}, "rbac": {"$ref": "#/$defs/RBAC", "description": "RBAC options for the virtual cluster."}, "plugins": {"anyOf": [{"patternProperties": {".*": {"type": "string"}}, "type": "object"}, {"type": "object"}, {"type": "object"}], "additionalProperties": {"$ref": "#/$defs/Plugins"}, "description": "Define which vCluster plugins to load."}, "experimental": {"$ref": "#/$defs/Experimental", "description": "Experimental features for vCluster. Configuration here might change, so be careful with this."}, "external": {"$ref": "#/$defs/ExternalConfig", "description": "External holds configuration for tools that are external to the vCluster."}, "telemetry": {"$ref": "#/$defs/Telemetry", "description": "Configuration related to telemetry gathered about vCluster usage."}, "serviceCIDR": {"type": "string", "description": "ServiceCIDR holds the service cidr for the virtual cluster. Do not use this option anymore."}, "pro": {"type": "boolean", "description": "Specifies whether to use vCluster Pro. This is automatically inferred in newer versions. Do not use that option anymore."}, "plugin": {"anyOf": [{"patternProperties": {".*": {"type": "string"}}, "type": "object"}, {"type": "object"}, {"type": "object"}], "additionalProperties": {"$ref": "#/$defs/Plugin"}, "description": "Plugin specifies which vCluster plugins to enable. Use \"plugins\" instead. Do not use this option anymore."}, "sleepMode": {"$ref": "#/$defs/SleepMode", "description": "SleepMode holds the native sleep mode configuration for Pro clusters"}, "logging": {"$ref": "#/$defs/Logging", "description": "Logging provides structured logging options"}}, "additionalProperties": false, "type": "object", "description": "Config is the vCluster config."}