controlPlane:
  advanced:
    virtualScheduler:
      enabled: true
  backingStore:
    etcd:
      deploy:
        enabled: true
        statefulSet:
          image:
            tag: 3.5.14-0
  distro:
    k8s:
      apiServer:
        extraArgs:
          - --service-account-jwks-uri=https://kubernetes.default.svc.cluster.local/openid/v1/jwks
        image:
          tag: v1.31.1
      controllerManager:
        image:
          tag: v1.31.1
      enabled: true
      scheduler:
        image:
          tag: v1.31.1
  statefulSet:
    scheduling:
      podManagementPolicy: OrderedReady

networking:
  advanced:
    proxyKubelets:
      byHostname: false
      byIP: false

sync:
  fromHost:
    csiDrivers:
      enabled: false
    csiStorageCapacities:
      enabled: false
    nodes:
      enabled: true
      selector:
        all: true
  toHost:
    persistentVolumes:
      enabled: true
    priorityClasses:
      enabled: true
    storageClasses:
      enabled: true
