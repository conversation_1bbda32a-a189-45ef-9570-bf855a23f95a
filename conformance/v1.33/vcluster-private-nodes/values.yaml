controlPlane:
  backingStore:
    etcd:
      deploy:
        enabled: true
        statefulSet:
          image:
            tag: 3.5.21-0
  distro:
    k8s:
      apiServer:
        extraArgs:
          - --service-account-jwks-uri=https://kubernetes.default.svc.cluster.local/openid/v1/jwks
      image:
        tag: v1.33.1
      enabled: true
  service:
    spec:
      type: LoadBalancer
privateNodes:
  enabled: true
