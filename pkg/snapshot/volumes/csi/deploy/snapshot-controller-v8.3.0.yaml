apiVersion: v1
kind: ServiceAccount
metadata:
  name: snapshot-controller
  namespace: kube-system
---
apiVersion: rbac.authorization.k8s.io/v1
kind: Role
metadata:
  name: snapshot-controller-leaderelection
  namespace: kube-system
rules:
- apiGroups:
  - coordination.k8s.io
  resources:
  - leases
  verbs:
  - get
  - watch
  - list
  - delete
  - update
  - create
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: snapshot-controller-runner
rules:
- apiGroups:
  - ""
  resources:
  - persistentvolumes
  verbs:
  - get
  - list
  - watch
- apiGroups:
  - ""
  resources:
  - persistentvolumeclaims
  verbs:
  - get
  - list
  - watch
  - update
- apiGroups:
  - ""
  resources:
  - events
  verbs:
  - list
  - watch
  - create
  - update
  - patch
- apiGroups:
  - snapshot.storage.k8s.io
  resources:
  - volumesnapshotclasses
  verbs:
  - get
  - list
  - watch
- apiGroups:
  - snapshot.storage.k8s.io
  resources:
  - volumesnapshotcontents
  verbs:
  - create
  - get
  - list
  - watch
  - update
  - delete
  - patch
- apiGroups:
  - snapshot.storage.k8s.io
  resources:
  - volumesnapshotcontents/status
  verbs:
  - patch
- apiGroups:
  - snapshot.storage.k8s.io
  resources:
  - volumesnapshots
  verbs:
  - create
  - get
  - list
  - watch
  - update
  - patch
  - delete
- apiGroups:
  - snapshot.storage.k8s.io
  resources:
  - volumesnapshots/status
  verbs:
  - update
  - patch
- apiGroups:
  - groupsnapshot.storage.k8s.io
  resources:
  - volumegroupsnapshotclasses
  verbs:
  - get
  - list
  - watch
- apiGroups:
  - groupsnapshot.storage.k8s.io
  resources:
  - volumegroupsnapshotcontents
  verbs:
  - create
  - get
  - list
  - watch
  - update
  - delete
  - patch
- apiGroups:
  - groupsnapshot.storage.k8s.io
  resources:
  - volumegroupsnapshotcontents/status
  verbs:
  - patch
- apiGroups:
  - groupsnapshot.storage.k8s.io
  resources:
  - volumegroupsnapshots
  verbs:
  - get
  - list
  - watch
  - update
  - patch
- apiGroups:
  - groupsnapshot.storage.k8s.io
  resources:
  - volumegroupsnapshots/status
  verbs:
  - update
  - patch
---
apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  name: snapshot-controller-leaderelection
  namespace: kube-system
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: Role
  name: snapshot-controller-leaderelection
subjects:
- kind: ServiceAccount
  name: snapshot-controller
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: snapshot-controller-role
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: snapshot-controller-runner
subjects:
- kind: ServiceAccount
  name: snapshot-controller
  namespace: kube-system
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: snapshot-controller
  namespace: kube-system
spec:
  minReadySeconds: 35
  replicas: 2
  selector:
    matchLabels:
      app.kubernetes.io/name: snapshot-controller
  strategy:
    rollingUpdate:
      maxSurge: 0
      maxUnavailable: 1
    type: RollingUpdate
  template:
    metadata:
      labels:
        app.kubernetes.io/name: snapshot-controller
    spec:
      containers:
      - args:
        - --v=5
        - --leader-election=true
        image: registry.k8s.io/sig-storage/snapshot-controller:v8.3.0
        imagePullPolicy: IfNotPresent
        name: snapshot-controller
      serviceAccountName: snapshot-controller
